"use client";
import { useState, useRef, useEffect } from "react";
import { DownIcon } from "../icon/Icon";

const PurposeDropDown = ({ title, initialValue }: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<string>(initialValue?.purpose || "All");
  const [selectedItem2, setSelectedItem2] = useState<string >(initialValue?.status || "All"  );

  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => setIsOpen((prev) => !prev);

  const handleSelect = (item: string) => {
    setSelectedItem(item);
    setIsOpen(false);
  };

  const handleSelect2 = (item: string) => {
    setSelectedItem2(item);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div ref={dropdownRef} tabIndex={0} className="dropdown w-full relative cursor-pointer rounded-lg border border- ring-1 ring-[#1d7eb6] font-inter px-2 py-2">
      <div className="flex justify-between items-center rounded-lg" onClick={toggleDropdown}>
        <div className="px-1 relative">
          <p className="text-xs">{title}</p>
          <button className="dropdown-button text-sm">
            {selectedItem !== "All" || selectedItem2 !== "All"
              ? `${selectedItem} , ${selectedItem2}`
              : initialValue?.purpose && initialValue?.status
              ? `${initialValue.purpose} , ${initialValue.status}`
              : "All"}
          </button>
        </div>
        <div className="pr-2">
          <DownIcon />
        </div>
      </div>

      {isOpen && (
        <div className="dropdown-menu absolute z-20 border top-14 rounded-lg border-[#E4E4E4] left-0 bg-slate-50 shadow-lg w-80 mt-1">
          <FilterForm 
            handleSelect={handleSelect} 
            handleSelect2={handleSelect2} 
            selectedPurpose={selectedItem} 
            selectedStatus={selectedItem2} 
          />
        </div>
      )}
    </div>
  );
};

export default PurposeDropDown;

// ✅ Updated FilterForm Component
function FilterForm({ handleSelect, handleSelect2, selectedPurpose, selectedStatus }: any) {
  const [purpose, setPurpose] = useState(selectedPurpose);
  const [status, setStatus] = useState(selectedStatus);

  useEffect(() => {
    setPurpose(selectedPurpose);
    setStatus(selectedStatus);
  }, [selectedPurpose, selectedStatus]);

  const purposeOptions = [
    { id: "All", label: "All" },
    { id: "Buy", label: "Buy" },
    { id: "Rent", label: "Rent" }
  ];

  const statusOptions = [
    { id: "All", label: "All" },
    { id: "Ready", label: "Ready" },
    { id: "Off-Plan", label: "Off-Plan" }
  ];

  const handleReset = () => {
    setPurpose("All");
    setStatus("All");
    handleSelect("All");
    handleSelect2("All");
  };

  const handleApply = () => {
    handleSelect(purpose);
    handleSelect2(status);
  };

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm">
      <TabGroup title="Purpose" options={purposeOptions} selectedId={purpose} onChange={setPurpose} />
      <TabGroup title="Completion Status" options={statusOptions} selectedId={status} onChange={setStatus} />
      
      <div className="flex justify-between mt-8 ps-4">
        <button onClick={handleReset} className="text-[#1d7eb6] font-medium hover:text-[#37a2db] transition-colors">
          Reset
        </button>
        <button onClick={handleApply} className="bg-[#1d7eb6] hover:bg-[#37a2db] text-white px-12 py-3 rounded-lg font-medium transition-colors">
          Apply
        </button>
      </div>
    </div>
  );
}

// ✅ TabGroup Component remains the same
interface TabOption {
  id: string;
  label: string;
}

interface TabGroupProps {
  title: string;
  options: TabOption[];
  selectedId: string;
  onChange: (id: string) => void;
}

function TabGroup({ title, options, selectedId, onChange }: TabGroupProps) {
  return (
    <div className="mb-6">
      <h2 className="text-lg font-medium text-gray-800 mb-3">{title}</h2>
      <div className="flex rounded-lg overflow-hidden border border-gray-200">
        {options.map((option, index) => (
          <button
            key={option.id}
            className={`flex-1 text-sm py-3 px-3 text-center transition-colors cursor-pointer ${
              selectedId === option.id ? "bg-[#993333] text-white" : "bg-white text-gray-600 hover:bg-[#ac4e4e] hover:text-white"
            } ${index === 1 || index === 0 ? "border-e border-gray-200" : ""}`}
            onClick={() => onChange(option.id)}
          >
            {option.label}
          </button>
        ))}
      </div>
    </div>
  );
}
