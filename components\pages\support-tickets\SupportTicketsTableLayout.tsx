'use client';


import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React from 'react';
import { useRouter } from 'next/navigation';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
import SupportTicketsTable from './SupportTicketsTable';

const SupportTicketsTableLayout = () => {
    const { push } = useRouter();

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Support Tickets"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Support Tickets' }]}
              
            />
            <div className="px-4">
                <SupportTicketsTable/>
            </div>
        </DefaultPageLayout>
    );
};

export default SupportTicketsTableLayout; 