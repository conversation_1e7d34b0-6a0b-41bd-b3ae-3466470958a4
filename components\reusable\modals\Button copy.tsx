"use client";
 
import React from "react";

 
 
export const Button = ({
  className,
  value,
  onClick,
  icon,
  textColor = "text-white",
  disabled,
}: any) => {
  return (
    <button
      className={`flex items-center justify-center gap-2 rounded-lg bg-white border border-[#1d7eb6] !text-[#1d7eb6] px-8 py-2 text-sm w-full hover:bg-[#1d7eb6] hover:!text-white transition-colors duration-300 ease-in-out
      ${textColor} ${className}`}
      disabled={disabled}
      onClick={onClick}
    >
      {icon ? icon : null}
      {value}
    </button>
  );
};

 