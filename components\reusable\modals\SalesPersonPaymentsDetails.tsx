"use client";

import API_ENDPOINTS from "@/app/lib/apiRoutes";
import {
  X,
  ReceiptText,
  DollarSign,
  Clock,
  Download,
  Mail,
  Users,
  TrendingUp,
} from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import SearchDropDown from "../SearchDropDown";
import { showMessage } from "@/app/lib/Alert";
import Loading from "@/components/layouts/loading";

type CommissionRecord = {
  unique_id: string;
  salesperson_id: number;
  salesperson_name: string;
  salesperson_email: string;
  month_year: string;
  month_date: string;
  total_commission: string;
  payment_method: string | null;
  payment_at: string | null;
  referal_payment_id: string | null;
  referal_payment_amount: string | null;
  subscription_price: string | null;
};
 
interface SalespersonDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  id: string | number;
}

export default function SalesPersonPaymentsDetails({
  isOpen,
  onClose,
  id,
}: SalespersonDetailsModalProps) {
  const [monthFilter, setMonthFilter] = useState<string>("All Months");

  const itemsPerPageOptions = [5, 10, 25, 50];
  const [pageSize, setPageSize] = useState<number>(10);
  const [page, setPage] = useState<number>(1);

  const [data, setData] = useState<CommissionRecord[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [hasServerPagination, setHasServerPagination] = useState<boolean>(false);

  const [monthOptions, setMonthOptions] = useState<string[]>([]);
  const [salesPerson, setSalesPerson] = useState<CommissionRecord | null>(null);

  const [commissionDetails, setCommissionDetails] = useState({
    total: 0,
    referal: 0,
    pending: 0,
    earned: 0,
  });

  const currency = (num: number) => {
    if (!isFinite(num)) return "-";
    return num.toLocaleString(undefined, {
      style: "currency",
      currency: "AED",
      maximumFractionDigits: 2,
    });
  };

  const safeDate = (d?: string | null) => {
    if (!d) return "-";
    const dt = new Date(d);
    if (isNaN(dt.getTime())) return String(d);
    return dt.toLocaleDateString();
  };

  const salespersonName =
    salesPerson?.salesperson_name || data[0]?.salesperson_name || "Agent";

  const fetchSalespersons = async () => {
    if (!id) return;
    setLoading(true);

    try {
      const params = new URLSearchParams();
      params.append("page", page.toString());
      params.append("pageSize", pageSize.toString());

      if (monthFilter !== "All Months") {
        params.append("monthAndYear", monthFilter);
      }

      const res = await fetch(
        `${API_ENDPOINTS.SALESPERSONS_COMMISSIONS}/payments/${id}?${params}`,
        { credentials: "include" }
      );
      const result = await res.json();

      const records: CommissionRecord[] = Array.isArray(result.data)
        ? result.data
        : [];

      const pagination = result.pagination ?? null;

      setData(records);
      setTotalCount(pagination?.total ?? records.length);
      setHasServerPagination(Boolean(pagination));

      setMonthOptions(
        result.availableMonths
          ? ["All Months", ...result.availableMonths]
          : ["All Months"]
      );

      setSalesPerson(records[0] ?? null);

      if (pagination) {
        if (typeof pagination.page === "number") setPage(pagination.page);
        if (typeof pagination.pageSize === "number") setPageSize(pagination.pageSize);
      }

      setCommissionDetails({
        total: result.totals.totalValue,
        referal: result.totals.totalReferrals,
        pending: result.totals.pending,
        earned: result.totals.commissionEarned,
      });


      
    } catch (err) {
      console.error("Failed to fetch salespersons:", err);
      setData([]);
      setTotalCount(0);
      setHasServerPagination(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSalespersons();
  }, [id, page, pageSize, monthFilter]);

  useEffect(() => {
    setPage(1);
  }, [monthFilter, pageSize]);

  const pageRows = useMemo(() => {
    if (hasServerPagination) return data;
    const start = (page - 1) * pageSize;
    return data.slice(start, start + pageSize);
  }, [data, page, pageSize, hasServerPagination]);

  const totalPages = Math.max(1, Math.ceil((totalCount || pageRows.length) / pageSize));
  const currentPage = page;

  const pager = useMemo(() => {
    const pages: (number | "...")[] = [];
    const delta = 2;
    const left = Math.max(1, currentPage - delta);
    const right = Math.min(totalPages, currentPage + delta);

    if (left > 1) {
      pages.push(1);
      if (left > 2) pages.push("...");
    }

    for (let p = left; p <= right; p++) pages.push(p);

    if (right < totalPages) {
      if (right < totalPages - 1) pages.push("...");
      pages.push(totalPages);
    }

    return pages;
  }, [currentPage, totalPages]);

  const handleDownloadCSV = () => {
    if (!pageRows || pageRows.length === 0) return;
  
    // Define the headers you want
    const headers = [
      { label: "Salesperson Name", value: "salesperson_name" },
      { label: "Salesperson Email", value: "salesperson_email" },
      { label: "Month Year", value: "month_year" },
      { label: "Total Commission", value: "total_commission" },
      { label: "Payment Method", value: "payment_method" },
      { label: "Payment Date", value: "payment_at" },
      { label: "Referral Payment Amount", value: "referal_payment_amount" },
      { label: "Subscription Price", value: "subscription_price" },
    ];
  
    // Map data into CSV rows
    const rows = pageRows.map((row) =>
      headers.map((h) => {
        let val = row[h.value as keyof CommissionRecord];
        
        if (h.value === "payment_at") {
          val = safeDate(val as string | null);
        }
        if (val === null || val === undefined) val = "-";
        
        const strVal = String(val).replace(/"/g, '""'); // escape quotes
        return strVal.includes(",") || strVal.includes("\n") ? `"${strVal}"` : strVal;
      })
    );
  
    // Build CSV string
    const csvContent = [
      headers.map((h) => h.label).join(","), // header row
      ...rows.map((r) => r.join(",")), // data rows
    ].join("\n");
  
    // Trigger download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `commission-statement-${String(salespersonName)
      .replace(/\s+/g, "-")
      .toLowerCase()}.csv`;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
  };
  

  const handleSendToAgent = async () => {
    if (!id) return;
    setLoading(true);

    try {
      const params = new URLSearchParams();
    

      if (monthFilter !== "All Months") {
        params.append("monthAndYear", monthFilter);
      }

      const res = await fetch(
        `${API_ENDPOINTS.SALESPERSONS_COMMISSIONS}/paymentsemail/${id}?${params}`,
        { credentials: "include" }
      );
      const result = await res.json();
      if (result.success) {
        showMessage("Commission Details sent successfully", "success");
      } else {
        showMessage(result.message, "error");
      } 
      
    } catch (err) {
      console.error("Failed to fetch salespersons:", err);
   
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
     {
      loading && <Loading />
    }
    <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative w-full max-w-[1100px] rounded-2xl bg-white shadow-lg max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between border-b px-6 pb-4 pt-6">
          <h2 className="text-lg font-semibold">Salesperson Details</h2>
          <button onClick={onClose} className="p-1 text-gray-400 hover:bg-gray-100 rounded-md">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6">
          {/* Title & Actions */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 border-b pb-4">
            <div className="flex items-center gap-3">
              <div className="rounded-xl border bg-neutral-100 p-2">
                <ReceiptText className="h-5 w-5 text-neutral-500" />
              </div>
              <h1 className="text-xl font-semibold">
                Commission Statement - {salesPerson?.salesperson_name || salespersonName}
              </h1>
            </div>
            <div className="flex items-center gap-2 mt-4 sm:mt-0">
              <button onClick={handleDownloadCSV} className="border px-3 py-2 rounded-md flex items-center gap-2">
                <Download className="h-4 w-4" /> Download CSV
              </button>
              <button
                onClick={handleSendToAgent}
                className="bg-black text-white px-3 py-2 rounded-md flex items-center gap-2"
              >
                <Mail className="h-4 w-4" /> Send to Agent
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="mb-4">
            <SearchDropDown
              classes="!h-14 w-full md:max-w-[287px]"
              dropdownOptions={monthOptions.map((m) => ({ label: m }))}
              initail={monthFilter}
              setSelectedStatus={(v: string) => setMonthFilter(v)}
            />
          </div>

    
          <div className="grid grid-cols-4 gap-4 mb-6">
        <StatCard title="Commission Earned" value={`AED ${commissionDetails.earned}`} icon={<DollarSign />} />
        <StatCard title="Pending" value={`AED ${commissionDetails.pending}`} icon={<Clock />} />
        <StatCard title="Total Referrals" value={commissionDetails.referal.toString()} icon={<Users />} />
        <StatCard title="Total Value" value={`AED ${commissionDetails.total}`} icon={<TrendingUp />} />
      </div>

          {/* Table */}
          <div className="rounded-xl border overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="bg-neutral-50 text-left">
                <tr>
                  <th className="px-4 py-3">Month</th>
                  <th className="px-4 py-3">Total Commission</th>
                  <th className="px-4 py-3">Payment Method</th>
                  <th className="px-4 py-3">Payment Date</th>
                  <th className="px-4 py-3">Referral Payment Amount</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={5} className="px-4 py-8 text-center text-neutral-500">
                      Loading...
                    </td>
                  </tr>
                ) : pageRows.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-4 py-8 text-center text-neutral-500">
                      No records found.
                    </td>
                  </tr>
                ) : (
                  pageRows.map((r) => (
                    <tr key={r.unique_id} className="border-t">
                      <td className="px-4 py-3">{r.month_year}</td>
                      <td className="px-4 py-3">{currency(Number(r.total_commission))}</td>
                      <td className="px-4 py-3">{r.payment_method ?? "-"}</td>
                      <td className="px-4 py-3">{safeDate(r.payment_at)}</td>
                      <td className="px-4 py-3">{r.subscription_price ?Number(r.subscription_price).toFixed(0) : "-"}</td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>

            {/* Pagination */}
            <div className="flex flex-col sm:flex-row items-center justify-between border-t p-3 gap-3">
              <div className="flex items-center gap-2">
                <label htmlFor="pageSize" className="text-sm">Rows per page</label>
                <select
                  id="pageSize"
                  className="h-8 rounded-md border bg-white px-2 text-sm"
                  value={pageSize}
                  onChange={(e) => setPageSize(Number(e.target.value))}
                >
                  {itemsPerPageOptions.map((n) => (
                    <option key={n} value={n}>{n}</option>
                  ))}
                </select>
              </div>

              <div className="flex items-center gap-1">
                <button
                  onClick={() => setPage((p) => Math.max(1, p - 1))}
                  disabled={currentPage === 1}
                  className="h-8 w-8 border rounded-md disabled:opacity-50"
                >
                  {"<"}
                </button>
                {pager.map((p, idx) =>
                  p === "..." ? (
                    <span key={idx} className="px-2">...</span>
                  ) : (
                    <button
                      key={p}
                      onClick={() => setPage(Number(p))}
                      className={`h-8 min-w-8 border rounded-md px-2 ${
                        p === currentPage ? "bg-black text-white" : ""
                      }`}
                    >
                      {p}
                    </button>
                  )
                )}
                <button
                  onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 border rounded-md disabled:opacity-50"
                >
                  {">"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
 
function StatCard({ title, value, icon }: { title: string; value: string; icon: React.ReactNode }) {
    return (
      <div className="flex items-center gap-4 border p-4 rounded shadow">
        <div className="text-blue-600">{icon}</div>
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          <p className="text-lg font-bold">{value}</p>
        </div>
      </div>
    );
  }