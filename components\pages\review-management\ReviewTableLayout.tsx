'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React from 'react';
import { useRouter } from 'next/navigation';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
import ReviewTable from './ReviewTable';

const ReviewTableLayout = () => {
    const { push } = useRouter();

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Review Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Reviews Management' }]}
            />

            <div className="px-4">
                <ReviewTable/>
            </div>
        </DefaultPageLayout>
    );
};

export default ReviewTableLayout;

