'use client';

import type React from 'react';
import { ActionIcon, DatatableAccendingSortingIcon, DatatableDeccendingSortingIcon, DatatableSortingIcon, PaginationDownIcon, PaginationLeftIcon, PaginationRightIcon } from '@/components/icon/Icon';
import { useState, useRef, useEffect } from 'react';
import Modal from '@/components/reusable/modals/modal';
import SearchInput from '@/components/reusable/SearchBar';
import { usePathname, useRouter } from 'next/navigation';
import Loading from '@/components/layouts/loading';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import DeleteReviewModal from './DeleteReviewModal';
import ViewReviewModal from './ViewReviewModal';
import AddNoteModal from './AddNoteModal';
import HideReviewModal from './HideReviewModal';
import { useToast } from '@/components/reusable/Notify';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import reviewService, { type Review, type ReviewStats } from '@/lib/reviewService';
import { showMessage } from '@/app/lib/Alert';
import { useDebouncedValue } from '@/store/utils.ts/functions';

const columns = [
    { key: 'reviewer', label: 'Reviewer', width: 'w-[25%]', sortable: true },
    { key: 'target', label: 'Target', width: 'w-[25%]', sortable: true },
    { key: 'rating', label: 'Rating', width: 'w-[15%]', sortable: true },
    { key: 'status', label: 'Status', width: 'w-[15%]', sortable: true },
    { key: 'date', label: 'Date', width: 'w-[15%]', sortable: true },
    { key: 'actions', label: 'Actions', width: 'w-[5%]', sortable: false },
];

 

const statusDropdown = [
    { label: 'All Statuses' },
    { label: 'Pending' },
    { label: 'Publish' },
    { label: 'Hidden' }
];

const typeDropdown = [
    { label: 'All Types' },
    { label: 'Agent' },
    { label: 'Agency' }
];

export default function ReviewTable() {
    const { push } = useRouter();
    const pathname = usePathname();
    const { showToast } = useToast();
    const [loader, setLoader] = useState(false); // Initial loading state
    const [reviewsPerPage, setReviewsPerPage] = useState(10);
    const [reviews, setReviews] = useState<Review[]>([]);
    const [pagination, setPagination] = useState({
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
    });
    const [isInitialLoad, setIsInitialLoad] = useState(true); // Track initial load state
    const [showDropdown, setShowDropdown] = useState(false);
    const [showActionDropdown, setShowActionDropdown] = useState<number | null>(null);
    const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');
    const [warningModal, setWarningModal] = useState(false);
    const [viewReviewModal, setViewReviewModal] = useState(false);
    const [addNoteModal, setAddNoteModal] = useState(false);
    const [hideReviewModal, setHideReviewModal] = useState(false);
    const [selectedReview, setSelectedReview] = useState<any>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedRecordId, setSelectedRecordId] = useState<number>();
    const [selectedStatus, setSelectedStatus] = useState('All Statuses');
    const [selectedType, setSelectedType] = useState('All Types');
    const [stats, setStats] = useState<ReviewStats>({
        totalReviews: 0,
        pendingReviews: 0,
        approvedReviews: 0,
        rejectedReviews: 0,
        averageRating: 0,
        hiddenReviews: 0,
        flaggedReviews: 0,
    });
    const [statsLoading, setStatsLoading] = useState(true);

    // Sorting state
    const [sortConfig, setSortConfig] = useState<{
        key: string;
        direction: 'ascending' | 'descending' | null;
    }>({
        key: '',
        direction: null,
    });

    // Improved debouncing using the project's standard hook
    const debouncedSearchTerm = useDebouncedValue(searchTerm, 300);

    const dropdownRef = useRef<HTMLDivElement>(null);
    const actionDropdownRef = useRef<HTMLDivElement>(null);
    const actionButtonRefs = useRef<Map<number, HTMLButtonElement>>(new Map());

    // Fetch reviews from API
    const fetchReviews = async (isFirstLoad = false) => {
        try {
            // Only show loader on initial load to prevent flickering
            if (isFirstLoad || isInitialLoad) {
                setLoader(true);
            }

            const response = await reviewService.getReviews(
                pagination.page,
                pagination.limit,
                debouncedSearchTerm,
                selectedStatus,
                selectedType,
                undefined // rating filter
            );

            if (response.success) {
                setReviews(response.data.reviews);
                setPagination(response.data.pagination);

                // Mark initial load as complete
                if (isInitialLoad) {
                    setIsInitialLoad(false);
                }
            }
        } catch (error) {
            console.error('Error fetching reviews:', error);
            showToast('Failed to fetch reviews.', 'error');
        } finally {
            // Only hide loader if it was shown
            if (isFirstLoad || isInitialLoad) {
                setLoader(false);
            }
        }
    };

    // Fetch stats from API
    const fetchStats = async () => {
        try {
            setStatsLoading(true);
            console.log('Fetching review stats...');
            const response = await reviewService.getStats();
            console.log('Stats API Response:', response);
            if (response.success) {
                console.log('Setting stats data:', response.data);
                setStats(response.data);
            } else {
                console.error('Stats API failed:', response.message);
            }
        } catch (error) {
            console.error('Error fetching stats:', error);
            showToast('Failed to fetch review statistics.', 'error');
        } finally {
            setStatsLoading(false);
        }
    };

    // Fetch reviews when component mounts or filters change
    useEffect(() => {
        fetchReviews(isInitialLoad);
    }, [pagination.page, pagination.limit, debouncedSearchTerm, selectedStatus, selectedType]);

    // Fetch stats when component mounts
    useEffect(() => {
        fetchStats();
    }, []);

    // Reset pagination to first page when search term changes
    useEffect(() => {
        if (searchTerm !== debouncedSearchTerm) {
            setPagination(prev => ({ ...prev, page: 1 }));
        }
    }, [debouncedSearchTerm]);



    // Client-side sorting function
    const sortReviews = (reviews: Review[], sortKey: string, direction: 'ascending' | 'descending' | null) => {
        if (!direction || !sortKey) return reviews;

        return [...reviews].sort((a, b) => {
            let aValue: any;
            let bValue: any;

            switch (sortKey) {
                case 'reviewer':
                    aValue = `${a.reviewerFirstName || ''} ${a.reviewerLastName || ''}`.trim() || a.reviewerEmail;
                    bValue = `${b.reviewerFirstName || ''} ${b.reviewerLastName || ''}`.trim() || b.reviewerEmail;
                    break;
                case 'target':
                    aValue = `${a.revieweeFirstName || ''} ${a.revieweeLastName || ''}`.trim() || a.revieweeEmail;
                    bValue = `${b.revieweeFirstName || ''} ${b.revieweeLastName || ''}`.trim() || b.revieweeEmail;
                    break;
                case 'rating':
                    aValue = a.rating;
                    bValue = b.rating;
                    break;
                case 'status':
                    aValue = a.statusName;
                    bValue = b.statusName;
                    break;
                case 'date':
                    aValue = new Date(a.created_at);
                    bValue = new Date(b.created_at);
                    break;
                default:
                    return 0;
            }

            // Handle different data types
            if (aValue instanceof Date && bValue instanceof Date) {
                return direction === 'ascending' ? aValue.getTime() - bValue.getTime() : bValue.getTime() - aValue.getTime();
            }

            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return direction === 'ascending' ? aValue - bValue : bValue - aValue;
            }

            // String comparison
            const aStr = String(aValue).toLowerCase();
            const bStr = String(bValue).toLowerCase();

            if (direction === 'ascending') {
                return aStr.localeCompare(bStr);
            } else {
                return bStr.localeCompare(aStr);
            }
        });
    };

    // Handle sorting
    const requestSort = (key: string) => {
        let direction: 'ascending' | 'descending' | null = 'ascending';

        if (sortConfig.key === key) {
            if (sortConfig.direction === 'ascending') {
                direction = 'descending';
            } else if (sortConfig.direction === 'descending') {
                direction = null;
            }
        }

        setSortConfig({ key, direction });
    };

    // Apply client-side sorting to the reviews
    const currentReviews = sortReviews(reviews, sortConfig.key, sortConfig.direction);
    const totalPages = pagination.totalPages;

    // Close dropdowns when clicking outside
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setShowDropdown(false);
            }
            if (actionDropdownRef.current && !actionDropdownRef.current.contains(event.target as Node)) {
                setShowActionDropdown(null);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Handle dropdown positioning
    const handleActionClick = (reviewId: number, event: React.MouseEvent<HTMLButtonElement>) => {
        actionButtonRefs.current.set(reviewId, event.currentTarget);

        if (showActionDropdown === reviewId) {
            setShowActionDropdown(null);
            return;
        }

        const buttonRect = event.currentTarget.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - buttonRect.bottom;

        setDropdownPosition(spaceBelow < 200 ? 'top' : 'bottom');
        setShowActionDropdown(reviewId);
    };

    const goToPage = (pageNumber: number) => setPagination(prev => ({ ...prev, page: pageNumber + 1 }));
    const handlePrev = () => setPagination(prev => ({ ...prev, page: Math.max(prev.page - 1, 1) }));
    const handleNext = () => setPagination(prev => ({ ...prev, page: Math.min(prev.page + 1, prev.totalPages) }));

    const paginationRange = () => {
        const range = [];
        const currentPage = pagination.page - 1; // Convert to 0-based for display
        let start = Math.max(currentPage - 1, 0);
        let end = Math.min(currentPage + 2, totalPages - 1);

        if (end - start < 3) {
            if (start === 0) {
                end = Math.min(start + 3, totalPages - 1);
            } else {
                start = Math.max(end - 3, 0);
            }
        }

        for (let i = start; i <= end; i++) {
            range.push(i);
        }

        return range;
    };

    const getSortIcon = (key: string) => {
        if (sortConfig.key !== key) {
            return <DatatableSortingIcon />;
        }

        if (sortConfig.direction === 'ascending') {
            return <DatatableAccendingSortingIcon />;
        }

        if (sortConfig.direction === 'descending') {
            return <DatatableDeccendingSortingIcon />;
        }

        return <DatatableSortingIcon />;
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        // Pagination reset is now handled automatically by useEffect when debouncedSearchTerm changes
    };

    // Filter change handlers that reset pagination
    const handleStatusChange = (status: string) => {
        setSelectedStatus(status);
        setPagination(prev => ({ ...prev, page: 1 }));
    };

    const handleTypeChange = (type: string) => {
        setSelectedType(type);
        setPagination(prev => ({ ...prev, page: 1 }));
    };

    const deleteReview = async (id: number) => {
        try {
            const response = await reviewService.deleteReview(id);

            if (response.success) {
                showMessage('Review deleted successfully.', 'success');
                fetchReviews(); // Refresh the list
                fetchStats(); // Refresh the stats
            } else {
                showToast(response.message || 'Failed to delete review.', 'error');
            }
        } catch (error) {
            console.error('Delete review error:', error);
            showToast('Failed to delete review.', 'error');
        }
    };

    const approveReview = async (id: number) => {
        try {
            const response = await reviewService.approveReview(id);

            if (response.success) {
                showMessage('Review approved and published.', 'success');
                fetchReviews(); // Refresh the list
                fetchStats(); // Refresh the stats
            } else {
                showToast(response.message || 'Failed to approve review.', 'error');
            }
        } catch (error) {
            console.error('Approve review error:', error);
            showToast('Failed to approve review.', 'error');
        }
    };

    const rejectReview = async (id: number) => {
        try {
            const response = await reviewService.rejectReview(id);

            if (response.success) {
                showToast('Review rejected.', 'success');
                fetchReviews(); // Refresh the list
                fetchStats(); // Refresh the stats
            } else {
                showToast(response.message || 'Failed to reject review.', 'error');
            }
        } catch (error) {
            console.error('Reject review error:', error);
            showToast('Failed to reject review.', 'error');
        }
    };

    const archiveReview = async (id: number) => {
        try {
            const response = await reviewService.archiveReview(id);

            if (response.success) {
                showToast('Review archived successfully.', 'success');
                fetchReviews(); // Refresh the list
                fetchStats(); // Refresh the stats
            } else {
                showToast(response.message || 'Failed to archive review.', 'error');
            }
        } catch (error) {
            console.error('Archive review error:', error);
            showMessage('Failed to archive review.', 'error');
        }
    };

    const hideReview = async (id: number, reason: string) => {
        try {
            const response = await reviewService.hideReview(id, reason);

            if (response.success) {
                showMessage('Review hidden successfully.', 'success');
                setHideReviewModal(false);
                setSelectedReview(null);
                fetchReviews(); // Refresh the list
                fetchStats(); // Refresh the stats
            } else {
                showToast(response.message || 'Failed to hide review.', 'error');
            }
        } catch (error) {
            console.error('Hide review error:', error);
            showToast('Failed to hide review.', 'error');
        }
    };

    const restoreReview = async (id: number) => {
        try {
            const response = await reviewService.restoreReview(id);

            if (response.success) {
                showMessage('Review restored successfully.', 'success');
                fetchReviews(); // Refresh the list
                fetchStats(); // Refresh the stats
            } else {
                showToast(response.message || 'Failed to restore review.', 'error');
            }
        } catch (error) {
            console.error('Restore review error:', error);
            showToast('Failed to restore review.', 'error');
        }
    };

    const flagReview = async (id: number) => {
        try {
            const response = await reviewService.flagReview(id);

            if (response.success) {
                showMessage('Review flagged successfully.', 'success');
                // Update the local state immediately for better UX
                setReviews(prev => prev.map(review =>
                    review.id === id ? { ...review, flagged: true } : review
                ));
                setShowActionDropdown(null);
                fetchReviews(); // Refresh the list to ensure sync with server
                fetchStats(); // Refresh the stats
            } else {
                showToast(response.message || 'Failed to flag review.', 'error');
            }
        } catch (error) {
            console.error('Flag review error:', error);
            showToast('Failed to flag review.', 'error');
        }
    };

    const unflagReview = async (id: number) => {
        try {
            const response = await reviewService.unflagReview(id);

            if (response.success) {
                showMessage('Review unflagged successfully.', 'success');
                // Update the local state immediately for better UX
                setReviews(prev => prev.map(review =>
                    review.id === id ? { ...review, flagged: false } : review
                ));
                setShowActionDropdown(null);
                fetchReviews(); // Refresh the list to ensure sync with server
                fetchStats(); // Refresh the stats
            } else {
                showToast(response.message || 'Failed to unflag review.', 'error');
            }
        } catch (error) {
            console.error('Unflag review error:', error);
            showToast('Failed to unflag review.', 'error');
        }
    };

    const updateReview = async (reviewData: any) => {
        try {
            await new Promise(resolve => setTimeout(resolve, 1000));

            setReviews(prev => prev.map(review =>
                review.id === reviewData.id ? reviewData : review
            ));
            showToast('Review updated successfully.', 'success');
        } catch (error) {
            console.error('Update review error:', error);
            showToast('Failed to update review.', 'error');
        }
    };

    const handleViewReview = (review: any) => {
        setSelectedReview(review);
        setViewReviewModal(true);
        setShowActionDropdown(null);
    };

    const handleEditReview = (review: any) => {
        // For now, just show a message that editing is disabled
        showToast('Direct editing is disabled. Use approve/reject/archive actions instead.', 'info');
        setShowActionDropdown(null);
    };

    const handleDeleteReview = (id: number) => {
        setSelectedRecordId(id);
        setWarningModal(true);
        setShowActionDropdown(null);
    };

    const handleApproveReview = (id: number) => {
        approveReview(id);
        setShowActionDropdown(null);
    };

    const handleRejectReview = (id: number) => {
        rejectReview(id);
        setShowActionDropdown(null);
    };

    const handleArchiveReview = (id: number) => {
        archiveReview(id);
        setShowActionDropdown(null);
    };

    const handleAddNote = (review: any) => {
        setSelectedReview(review);
        setAddNoteModal(true);
        setShowActionDropdown(null);
    };

    const handleHideReview = (review: any) => {
        setSelectedReview(review);
        setHideReviewModal(true);
        setShowActionDropdown(null);
    };

    const handleRestoreReview = (id: number) => {
        restoreReview(id);
        setShowActionDropdown(null);
    };

    const handleFlagReview = (id: number) => {
        flagReview(id);
    };

    const handleUnflagReview = (id: number) => {
        unflagReview(id);
    };

    const addNoteToReview = async (note: string) => {
        try {
            const response = await reviewService.addNote(selectedReview.id, note);

            if (response.success) {
                showMessage('Note added successfully.', 'success');
                setAddNoteModal(false);
                setSelectedReview(null);
                // Refresh the reviews list to get updated comment history
                fetchReviews();
            } else {
                showToast(response.message || 'Failed to add note.', 'error');
            }
        } catch (error) {
            console.error('Add note error:', error);
            showToast('Failed to add note.', 'error');
        }
    };

    const getStatusBadge = (status: string) => {
        const statusClasses = {
            'Publish': 'bg-green-500 text-white',
            'Pending': 'bg-[#fff3cd] text-[#856404] border border-[#ffeaa7]',
            'Rejected': 'bg-red-500 text-white',
            'Archived': 'bg-gray-500 text-white',
            'Hidden': 'bg-orange-500 text-white'
        };

        const showWarningIcon = status === 'Pending' || status === 'Flagged';

        return (
            <div className="flex items-center gap-1">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800'}`}>
                    {status}
                </span>
                {showWarningIcon && (
                    <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                )}
            </div>
        );
    };

    const getRatingStars = (rating: number) => {
        return (
            <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                    <svg
                        key={star}
                        className={`w-4 h-4 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                ))}
                <span className="text-sm text-gray-600 ml-1">({rating})</span>
            </div>
        );
    };

    // Use stats from API instead of local calculations
    const totalReviews = stats.totalReviews;
    const publishedCount = stats.approvedReviews; // approved = published
    const pendingCount = stats.pendingReviews;
    const rejectedCount = stats.rejectedReviews;
    const hiddenCount = stats.hiddenReviews || 0; // Get from API response
    const flaggedCount = stats.flaggedReviews || 0; // Get from API response
    const averageRating = stats.averageRating.toFixed(1);

    console.log('Current stats state:', stats);
    console.log('Display values:', {
        totalReviews,
        publishedCount,
        pendingCount,
        rejectedCount,
        hiddenCount,
        flaggedCount,
        averageRating
    });

    return (
        <div className="panel">
            {/* Statistics Cards - Always visible */}
            <div className="mb-8 rounded-lg bg-white pt-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-6">
                    {/* Total Reviews */}
                    {/* Total Reviews */}
                    <div className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-blue-200">
                        <div className="flex flex-col items-start justify-start gap-2">
                            <div className="font-golosText text-5xl font-bold text-blue-600">
                                {statsLoading ? '...' : totalReviews}
                            </div>
                            <div className="font-inter text-base font-medium text-[#555]">Total Reviews</div>
                        </div>
                    </div>

                    {/* Published Reviews */}
                    <div className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-green-200">
                        <div className="flex flex-col items-start justify-start gap-2">
                            <div className="font-golosText text-5xl font-bold text-green-600">
                                {statsLoading ? '...' : publishedCount}
                            </div>
                            <div className="font-inter text-base font-medium text-[#555]">Published</div>
                        </div>
                    </div>

                    {/* Pending Reviews */}
                    <div className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-yellow-200">
                        <div className="flex flex-col items-start justify-start gap-2">
                            <div className="font-golosText text-5xl font-bold text-yellow-600">
                                {statsLoading ? '...' : pendingCount}
                            </div>
                            <div className="font-inter text-base font-medium text-[#555]">Pending</div>
                        </div>
                    </div>

                    {/* Flagged Reviews */}
                    <div className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-red-200">
                        <div className="flex flex-col items-start justify-start gap-2">
                            <div className="font-golosText text-5xl font-bold text-red-600">
                                {statsLoading ? '...' : flaggedCount}
                            </div>
                            <div className="font-inter text-base font-medium text-[#555]">Flagged</div>
                        </div>
                    </div>

                    {/* Hidden Reviews */}
                    <div className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-purple-200">
                        <div className="flex flex-col items-start justify-start gap-2">
                            <div className="font-golosText text-5xl font-bold text-purple-600">
                                {statsLoading ? '...' : hiddenCount}
                            </div>
                            <div className="font-inter text-base font-medium text-[#555]">Hidden</div>
                        </div>
                    </div>

                    {/* Average Rating */}
                    <div className="flex items-center justify-between rounded-[20px] border-2 border-[#f0f0f0] bg-[#f8f9fa] p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition hover:border-orange-200">
                        <div className="flex flex-col items-start justify-start gap-2">
                            <div className="font-golosText text-5xl font-bold text-orange-600">
                                {statsLoading ? '...' : averageRating}
                            </div>
                            <div className="font-inter text-base font-medium text-[#555]">Avg Rating</div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Filters */}
            <div className="mb-8">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    <div className="w-full">
                        <SearchInput
                            onChange={handleSearchChange}
                            value={searchTerm}
                            placeholder="Search reviews..."
                        />
                    </div>
                    <div className="w-full">
                        <SearchDropDown
                            classes="!h-14 w-full"
                            dropdownOptions={statusDropdown}
                            initail={selectedStatus}
                            setSelectedStatus={handleStatusChange}
                        />
                    </div>
                    <div className="w-full">
                        <SearchDropDown
                            classes="!h-14 w-full"
                            dropdownOptions={typeDropdown}
                            initail={selectedType}
                            setSelectedStatus={handleTypeChange}
                        />
                    </div>
                </div>
            </div>

            <div className="pb-4 font-inter font-normal leading-[30px]">
                <span className="text-[#636363]">You have </span>
                <span className="text-[#1D7EB6]">{totalReviews} Reviews</span>
            </div>

            <div className="bg-white">
                <div className="relative rounded-lg border shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)] overflow-visible">
                    <table className="w-full border-collapse font-inter">
                        <thead className="sticky top-0 z-[5] bg-[#e4e4e4]">
                            <tr className="w-full rounded-lg border-none">
                                {columns.map((column) => (
                                    <th
                                        key={column.key}
                                        className={`h-[72px] cursor-pointer border-none px-4 py-4 text-left font-inter text-base font-medium leading-normal text-[#2d2d2e] ${column.width}`}
                                        onClick={() => column.sortable && requestSort(column.key)}
                                    >
                                        <span className="flex items-center">
                                            {column.label}
                                            {column.sortable && getSortIcon(column.key)}
                                        </span>
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody className="max-h-[700px] overflow-y-auto overflow-x-visible">
                            {currentReviews.length === 0 && !loader ? (
                                <tr>
                                    <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                                        <div className="flex flex-col items-center justify-center">
                                            <svg className="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            <p className="text-lg font-medium text-gray-900">No reviews found</p>
                                            <p className="text-sm text-gray-500 mt-1">
                                                {debouncedSearchTerm || selectedStatus !== 'All Statuses' || selectedType !== 'All Types'
                                                    ? 'Try adjusting your search criteria or filters'
                                                    : 'No reviews have been submitted yet'
                                                }
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                            ) : (
                                currentReviews.map((review) => (
                                <tr key={review.id} className="hover:bg-gray-50">
                                    <td className="px-4 py-3 relative">
                                        <div>
                                            <div className="font-medium text-gray-900 flex items-center gap-2">
                                                {review.reviewerFirstName && review.reviewerLastName
                                                    ? `${review.reviewerFirstName} ${review.reviewerLastName}`
                                                    : 'N/A'}
                                                {review.flagged && (
                                                    <div className="relative group">
                                                        <svg
                                                            className="w-4 h-4 text-red-500 cursor-help"
                                                            fill="currentColor"
                                                            viewBox="0 0 24 24"
                                                        >
                                                            <path d="M14.4 6L14 4H5v17h2v-7h5.6l.4 2h7V6z" />
                                                        </svg>
                                                        {/* Enhanced tooltip with better positioning and z-index */}
                                                        <div className="absolute left-1/2 transform -translate-x-1/2 bottom-full mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 pointer-events-none whitespace-nowrap z-[999999]">
                                                            This review has been flagged
                                                            {/* Arrow pointing down */}
                                                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[4px] border-r-[4px] border-t-[4px] border-transparent border-t-gray-900"></div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="text-sm text-gray-500">{review.reviewerEmail}</div>
                                        </div>
                                    </td>
                                    <td className="px-4 py-3">
                                        <div className="font-medium text-gray-900">
                                            {review.revieweeFirstName && review.revieweeLastName
                                                ? `${review.revieweeFirstName} ${review.revieweeLastName}`
                                                : review.revieweeEmail}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {review.revieweeType === 'Individual' ? 'Individual' : 'Agency'}
                                        </div>
                                    </td>
                                    <td className="px-4 py-3">
                                        {getRatingStars(review.rating)}
                                    </td>
                                    <td className="px-4 py-3">
                                        {getStatusBadge(review.statusName)}
                                    </td>
                                    <td className="px-4 py-3 text-sm text-gray-500">
                                        {new Date(review.created_at).toLocaleDateString()}
                                    </td>
                                    <td className="px-4 py-3 text-right text-sm font-medium relative">
                                        <button
                                            ref={el => {
                                                if (el) actionButtonRefs.current.set(review.id, el);
                                            }}
                                            onClick={(e) => handleActionClick(review.id, e)}
                                            className="text-gray-400 hover:text-gray-600 p-1"
                                            title="Review actions"
                                        >
                                            <ActionIcon />
                                        </button>

                                        {showActionDropdown === review.id && (
                                            <div
                                                ref={actionDropdownRef}
                                                className={`absolute right-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10 ${
                                                    dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full'
                                                }`}
                                            >
                                                <div className="py-1">
                                                    <button
                                                        onClick={() => handleViewReview(review)}
                                                        className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    >
                                                        <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                        </svg>
                                                        View
                                                    </button>



                                                    {review.statusName === 'Pending' && (
                                                        <>
                                                            <button
                                                                onClick={() => handleApproveReview(review.id)}
                                                                className="flex items-center w-full text-left px-4 py-2 text-sm text-green-600 hover:bg-green-50"
                                                            >
                                                                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                                </svg>
                                                                Approve & Publish
                                                            </button>

                                                        </>
                                                    )}

                                                    <button
                                                        onClick={() => handleAddNote(review)}
                                                        className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    >
                                                        <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                        </svg>
                                                        Add Note
                                                    </button>

                                                    {/* Dynamic Flag/Unflag Toggle Button */}
                                                    {review.flagged ? (
                                                        <button
                                                            onClick={() => handleUnflagReview(review.id)}
                                                            className="flex items-center w-full text-left px-4 py-2 text-sm text-green-600 hover:bg-green-50"
                                                        >
                                                            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                            </svg>
                                                            Unflag Review
                                                        </button>
                                                    ) : (
                                                        <button
                                                            onClick={() => handleFlagReview(review.id)}
                                                            className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                                                        >
                                                            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 2h7a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2z" />
                                                            </svg>
                                                            Flag Review
                                                        </button>
                                                    )}

                                                    {review.statusName === 'Hidden' ? (
                                                        <button
                                                            onClick={() => handleRestoreReview(review.id)}
                                                            className="flex items-center w-full text-left px-4 py-2 text-sm text-green-600 hover:bg-green-50"
                                                        >
                                                            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                            </svg>
                                                            Restore
                                                        </button>
                                                    ) : (
                                                        <button
                                                            onClick={() => handleHideReview(review)}
                                                            className="flex items-center w-full text-left px-4 py-2 text-sm text-yellow-600 hover:bg-yellow-50"
                                                        >
                                                            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L6.5 6.5m3.378 3.378a3 3 0 013.243-2.878m0 0L16.5 4.5m-3.379 2.878L9.878 9.878m4.242 4.242L16.5 16.5m-2.378-3.378a3 3 0 01-4.243-4.243m0 0L6.5 6.5" />
                                                            </svg>
                                                            Hide
                                                        </button>
                                                    )}

                                                    <div className="border-t border-gray-100 my-1"></div>

                                                    <button
                                                        onClick={() => handleDeleteReview(review.id)}
                                                        className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                                                    >
                                                        <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                        </svg>
                                                        Delete
                                                    </button>
                                                </div>
                                            </div>
                                        )}
                                    </td>
                                </tr>
                                ))
                            )}
                        </tbody>
                    </table>

                    {/* Loading Overlay - only shows during initial load */}
                    {loader && (
                        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-75">
                            <Loading />
                        </div>
                    )}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                    <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
                        <div className="flex justify-between flex-1 sm:hidden">
                                                            <button
                                    onClick={handlePrev}
                                    disabled={pagination.page === 1}
                                    className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                                >
                                    Previous
                                </button>
                                <button
                                    onClick={handleNext}
                                    disabled={pagination.page >= totalPages}
                                    className="relative ml-3 inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                                >
                                    Next
                                </button>
                        </div>

                        <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing <span className="font-medium">{((pagination.page - 1) * pagination.limit) + 1}</span> to{' '}
                                    <span className="font-medium">
                                        {Math.min(pagination.page * pagination.limit, pagination.total)}
                                    </span>{' '}
                                    of <span className="font-medium">{pagination.total}</span> results
                                </p>
                            </div>

                            <div className="flex items-center gap-2">
                                <button
                                    onClick={handlePrev}
                                    disabled={pagination.page === 1}
                                    className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                                    title="Previous page"
                                >
                                    <PaginationLeftIcon />
                                </button>

                                {paginationRange().map((pageNumber) => (
                                    <button
                                        key={pageNumber}
                                        onClick={() => goToPage(pageNumber)}
                                        className={`px-3 py-1 text-sm font-medium rounded-md ${
                                            pagination.page === pageNumber + 1
                                                ? 'bg-orange-500 text-white'
                                                : 'text-gray-700 hover:bg-gray-100'
                                        }`}
                                    >
                                        {pageNumber + 1}
                                    </button>
                                ))}

                                <button
                                    onClick={handleNext}
                                    disabled={pagination.page >= totalPages}
                                    className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                                    title="Next page"
                                >
                                    <PaginationRightIcon />
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Modals */}
            <Modal isOpen={warningModal} onClose={() => setWarningModal(false)}>
                <DeleteReviewModal
                    review={reviews.find(r => r.id === selectedRecordId)}
                    onClose={() => setWarningModal(false)}
                    onConfirm={() => {
                        if (selectedRecordId) {
                            deleteReview(selectedRecordId);
                            setWarningModal(false);
                        }
                    }}
                />
            </Modal>

            <Modal isOpen={viewReviewModal} onClose={() => setViewReviewModal(false)}>
                <ViewReviewModal
                    review={selectedReview}
                    onClose={() => setViewReviewModal(false)}
                    onDelete={(id) => {
                        setViewReviewModal(false);
                        handleDeleteReview(id);
                    }}
                    onApprove={(id) => {
                        setViewReviewModal(false);
                        handleApproveReview(id);
                    }}
                    onReject={(id) => {
                        setViewReviewModal(false);
                        handleRejectReview(id);
                    }}
                    onHide={(id) => {
                        setViewReviewModal(false);
                        const review = reviews.find(r => r.id === id);
                        if (review) {
                            handleHideReview(review);
                        }
                    }}
                />
            </Modal>

            <Modal isOpen={addNoteModal} onClose={() => setAddNoteModal(false)}>
                <AddNoteModal
                    review={selectedReview}
                    onClose={() => {
                        setAddNoteModal(false);
                        setSelectedReview(null);
                    }}
                    onConfirm={addNoteToReview}
                />
            </Modal>

            <Modal isOpen={hideReviewModal} onClose={() => setHideReviewModal(false)}>
                <HideReviewModal
                    review={selectedReview}
                    onClose={() => {
                        setHideReviewModal(false);
                        setSelectedReview(null);
                    }}
                    onConfirm={(reason: string) => hideReview(selectedReview?.id, reason)}
                />
            </Modal>
        </div>
    );
}
