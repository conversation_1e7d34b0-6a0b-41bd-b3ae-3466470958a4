'use client';

import type React from 'react';
import '@/styles/ckeditor.css';
import { useEffect, useMemo, useRef, useState } from 'react';
import SearchDropDown from '@/components/reusable/SearchDropDown';

import uploadFile from '@/components/reusable/UploadFile';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { showMessage } from '@/app/lib/Alert';
import { CrossIcon } from '@/components/icon/Icon';

interface CreatePostModalProps {
    onClose: () => void;
    onSubmit: (postData: any) => void;
    fetchBlogPosts: () => void;
    post?: any;
    mode?: 'create' | 'edit';
}

/** ---------- TOC & IMG Helpers ---------- */

const HEADING_SELECTOR = 'h2, h3, h4';

function canUseDOM() {
    return typeof window !== 'undefined' && typeof document !== 'undefined';
}

// Slug for heading ids
function slugify(text: string) {
    return (text || '')
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .slice(0, 80);
}

function escapeHtml(s: string) {
    return (s || '').replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
}

type TocItem = { id: string; text: string; level: number };

// Does content already contain a TOC block?
function hasExistingToc(html: string): boolean {
    if (!canUseDOM() || typeof DOMParser === 'undefined') return false;
    const doc = new DOMParser().parseFromString(html || '', 'text/html');
    return !!doc.querySelector('nav.toc, .toc nav, .toc');
}

// Extract headings for TOC (skip anything inside the .toc block)
function extractHeadingsForToc(html: string): TocItem[] {
    if (!canUseDOM() || typeof DOMParser === 'undefined') return [];
    const parser = new DOMParser();
    const doc = parser.parseFromString(html || '', 'text/html');
    const nodes = Array.from(doc.querySelectorAll(HEADING_SELECTOR)).filter((n) => !(n as HTMLElement).closest('.toc'));

    const used = new Set<string>();

    return nodes.map((node, idx) => {
        const level = Number(node.tagName.replace('H', '')) || 2;
        const base = (node as HTMLElement).id || slugify(node.textContent || '') || `heading-${idx}`;
        let id = base;
        let bump = 1;
        while (used.has(id)) id = `${base}-${bump++}`;
        used.add(id);
        return { id, text: node.textContent || '', level };
    });
}

// Add ids to headings (skip headings inside .toc)
function injectIdsIntoHeadings(html: string): string {
    if (!canUseDOM() || typeof DOMParser === 'undefined') return html;
    const parser = new DOMParser();
    const doc = parser.parseFromString(html || '', 'text/html');
    const nodes = Array.from(doc.querySelectorAll(HEADING_SELECTOR)).filter((n) => !(n as HTMLElement).closest('.toc'));

    const used = new Set<string>();
    nodes.forEach((node, idx) => {
        const text = node.textContent || '';
        const base = (node as HTMLElement).id || slugify(text) || `heading-${idx}`;
        let id = base;
        let bump = 1;
        while (used.has(id)) id = `${base}-${bump++}`;
        used.add(id);
        (node as HTMLElement).id = id;
    });

    return doc.body.innerHTML;
}

// Enforce <img width="100%"> and remove width/height attrs
function normalizeImagesToFullWidth(html: string): string {
    if (!canUseDOM() || typeof DOMParser === 'undefined') {
        // regex fallback (best-effort)
        return html?.replace(/<img\b([^>]*?)>/gi, (_m, attrs) => {
            // remove width/height attributes
            let a = attrs.replace(/\s(width|height)="[^"]*"/gi, '');
            // inject width="100%" if not present
            if (!/\bwidth="/i.test(a)) a = ` width="100%" ${a}`.trim();
            return `<img ${a}>`;
        });
    }

    const doc = new DOMParser().parseFromString(html || '', 'text/html');
    doc.querySelectorAll('img').forEach((img) => {
        img.removeAttribute('height');
        img.removeAttribute('width');
        // also avoid inline width in style
        const style = img.getAttribute('style') || '';
        if (style) {
            const cleaned = style
                .split(';')
                .map((s) => s.trim())
                .filter((s) => s && !/^width\s*:/.test(s) && !/^max-width\s*:/.test(s));
            if (cleaned.length) img.setAttribute('style', cleaned.join('; '));
            else img.removeAttribute('style');
        }
        img.setAttribute('width', '100%'); // inject only width
    });
    return doc.body.innerHTML;
}

// Build TOC HTML block
function buildTocHtml(items: TocItem[]) {
    const itemsHtml = items.map((it) => `<li class="toc-li level-${it.level}"><a href="#${it.id}">${escapeHtml(it.text)}</a></li>`).join('');

    const style = `
<style>
  .toc { padding: 1rem; border: 1px solid #e5e7eb; border-radius: .5rem; background: #fafafa; }
  .toc h2 { margin: 0 0 .5rem 0; font-size: 1rem; font-weight: 600; }
  .toc .toc-list { list-style: none; padding: 0; margin: 0; }
  .toc .toc-li { margin: .25rem 0; }
  .toc .toc-li.level-3 { margin-left: 1rem; }
  .toc .toc-li.level-4 { margin-left: 2rem; }
  .toc .toc-li a { text-decoration: none; color: #1d7eb6; }
</style>`.trim();

    return `
${style}
<nav class="toc" aria-label="Table of contents">
  <h2>Table of contents</h2>
  <ul class="toc-list">
    ${itemsHtml}
  </ul>
</nav>`.trim();
}

/** -------------------------------- */

export default function CreatePostModal({ onClose, onSubmit, post, fetchBlogPosts, mode = 'create' }: CreatePostModalProps) {
    const [editorContent, setEditorContent] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(mode === 'edit');

    // Lazy-loaded CKEditor bits (avoid SSR/window errors)
    const editorPkgRef = useRef<any>(null); // { CKEditor }
    const buildRef = useRef<any>(null); // ClassicEditor class
    const [editorReady, setEditorReady] = useState(false);

    // Toggle
    const [insertTocOnSave, setInsertTocOnSave] = useState<boolean>(true);

    const [formData, setFormData] = useState<any>({
        title: '',
        author: '',
        excerpt: '',
        content: '',
        tags: '',
        featuredImage: '',
        category: 'Select category',
        featureBlog: false,
    });

    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [status, setStatus] = useState(mode === 'create' ? 22 : 23);

    const categories = ['Select category', 'Market Trends', 'Agent Tips', 'Investment', 'Education', 'Technology'];

    const statusOptions = [
        { label: 'Draft', value: 23 },
        { label: 'Published', value: 22 },
        { label: 'Archived', value: 5 },
    ];

    // Load CKEditor only in browser
    useEffect(() => {
        let mounted = true;
        (async () => {
            if (!canUseDOM()) return;
            try {
                const reactPkg = await import('@ckeditor/ckeditor5-react');
                const classicBuild = (await import('@ckeditor/ckeditor5-build-classic')).default;
                if (!mounted) return;
                editorPkgRef.current = reactPkg;
                buildRef.current = classicBuild;
                setEditorReady(true);
            } catch (err) {
                console.error('Failed to load CKEditor:', err);
                showMessage('Editor failed to load. Please refresh.', 'error');
            }
        })();
        return () => {
            mounted = false;
        };
    }, []);

    // Live TOC preview (built from content but ignoring headings inside .toc)
    const liveTocItems = useMemo<TocItem[]>(() => extractHeadingsForToc(editorContent), [editorContent]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData((prev: any) => ({ ...prev, [name]: value }));
        if (errors[name]) setErrors((prev) => ({ ...prev, [name]: '' }));
    };

    const validateForm = () => {
        const newErrors: { [key: string]: string } = {};
        if (!formData.title.trim()) newErrors.title = 'Please fill out this field.';
        if (!formData.author.trim()) newErrors.author = 'Please fill out this field.';
        if (!formData.category || formData.category === 'Select category') newErrors.category = 'Please select a category.';
        if (!formData.excerpt.trim()) newErrors.excerpt = 'Please fill out this field.';
        if (!formData.content.trim()) newErrors.content = 'Please fill out this field.';
        if (status === null) newErrors.status = 'Please select a status.';
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    function removeExistingToc(html: string): string {
        if (!canUseDOM() || typeof DOMParser === 'undefined') return html;
        const parser = new DOMParser();
        const doc = parser.parseFromString(html || '', 'text/html');
        doc.querySelectorAll('.toc').forEach((el) => el.remove());
        return doc.body.innerHTML;
    }

    function buildFinalContentForSave(rawHtml: string) {
        if (!canUseDOM()) return rawHtml;

        // 1) Remove any old TOC
        let html = removeExistingToc(rawHtml);

        // 2) Add ids to headings
        html = injectIdsIntoHeadings(html);

        // 3) Normalize images
        html = normalizeImagesToFullWidth(html);

        // 4) Insert fresh TOC if requested
        if (insertTocOnSave) {
            const items = extractHeadingsForToc(html);
            if (items.length) {
                const tocHtml = buildTocHtml(items);
                html = `${tocHtml}\n${html}`;
            }
        }
        return html;
    }

    const handleSubmit = async () => {
        if (!validateForm()) return;

        try {
            let imageUrl = '';
            try {
                if (formData.featuredImage instanceof File) {
                    imageUrl = await uploadFile(formData.featuredImage, 'featuredBlogImage');
                }
            } catch (error) {
                console.error('Error uploading image:', error);
            }

            const finalContent = buildFinalContentForSave(formData.content);

            const payload = {
                title: formData.title,
                author: formData.author,
                excerpt: formData.excerpt,
                content: finalContent,
                category: formData.category,
                featuredImage: imageUrl,
                statusId: status,
                tags: formData.tags,
                featureBlog: formData.featureBlog ? 'yes' : 'no',
            };

            const response = await fetch(API_ENDPOINTS.BLOG_CREATE, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify(payload),
            });

            const result = await response.json();
            if (!result.success) {
                showMessage('There was an error creating the blog. Please try again.', 'error');
                fetchBlogPosts();
            } else {
                onSubmit(result);
                onClose();
            }
        } catch (error) {
            console.error('Error creating blog:', error);
            showMessage('There was an error creating the blog. Please try again.', 'error');
        }
    };

    useEffect(() => {
        const fetchBlogOne = async () => {
            try {
                if (!post?.slug) {
                    setLoading(false);
                    return;
                }
                const response = await fetch(API_ENDPOINTS.BLOG_GET_ONE + post.slug, {
                    method: 'GET',
                    credentials: 'include',
                });

                const result = await response.json();
                if (result.success) {
                    const data = result.data;
                    setFormData({
                        title: data.title,
                        author: data.author,
                        excerpt: data.excerpt,
                        content: data.content,
                        category: data.category,
                        featuredImage: data.featuredImage,
                        status: data.statusId === 22 ? 'Published' : data.statusId === 23 ? 'Draft' : 'Archived',
                        tags: data.tags,
                        featureBlog: data.featured,
                    });
                    setEditorContent(typeof data.content === 'string' ? data.content : '');
                    setStatus(data.statusId);
                } else {
                    console.log('Blog fetch failed:', result);
                }
            } catch (error) {
                console.error('Error fetching blog:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchBlogOne();
    }, [post?.id, post?.slug]);

    const updatePost = async () => {
        if (!validateForm()) return;
        try {
            let imageUrl = formData.featuredImage;
            if (formData.featuredImage instanceof File) {
                imageUrl = await uploadFile(formData.featuredImage, 'featuredBlogImage');
            }

            const finalContent = buildFinalContentForSave(formData.content);

            const payload = {
                title: formData.title,
                author: formData.author,
                excerpt: formData.excerpt,
                content: finalContent,
                category: formData.category,
                featuredImage: imageUrl,
                statusId: status,
                tags: formData.tags,
                featureBlog: formData.featureBlog ? 'yes' : 'no',
            };

            const response = await fetch(API_ENDPOINTS.BLOG_UPDATE + post.id, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify(payload),
            });
            if (!response.ok) throw new Error('Failed to update blog');
            const result = await response.json();
            if (!result.success) {
                showMessage('There was an error updating the blog. Please try again.', 'error');
                return;
            } else {
                onSubmit(result);
                fetchBlogPosts();
                onClose();
            }
        } catch (error) {
            console.error('Error updating blog:', error);
            showMessage('There was an error updating the blog. Please try again.', 'error');
        }
    };

    const handleSubmitPost = () => {
        if (mode === 'edit') updatePost();
        else handleSubmit();
    };

    if (loading) return <>Loading</>;

    const CKEditorPkg = editorPkgRef.current?.CKEditor;
    const ClassicBuild = buildRef.current;

    return (
        <div className="mx-auto w-full max-w-4xl rounded-lg bg-white">
            <div className="flex items-center justify-end">
                <button onClick={onClose} className="flex h-6 w-6 items-center justify-center rounded-full text-xl text-gray-400 hover:bg-gray-100 hover:text-gray-600">
                    <CrossIcon />
                </button>
            </div>

            <div className="border-b border-gray-200" onClick={(e) => e.stopPropagation()}>
                <div className="flex items-center justify-start">
                    <h2 className="font-inter text-lg font-semibold text-[#2d2d2e]">{mode === 'edit' ? 'Edit Blog Post' : 'Create New Blog Post'}</h2>
                </div>
            </div>

            <div className="space-y-4 py-4" onClick={(e) => e.stopPropagation()}>
                {/* Title */}
                <div>
                    <label className="mb-2 block text-sm font-medium">Title</label>
                    <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Enter post title"
                        className={`w-full rounded-md border px-3 py-2 text-sm ${errors.title ? 'border-red-500' : 'border-gray-300'}`}
                    />
                    {errors.title && <p className="text-xs text-red-600">{errors.title}</p>}
                </div>

                {/* Author */}
                <div>
                    <label className="mb-2 block text-sm font-medium">Author</label>
                    <input
                        type="text"
                        name="author"
                        value={formData.author}
                        onChange={handleInputChange}
                        className={`w-full rounded-md border px-3 py-2 text-sm ${errors.author ? 'border-red-500' : 'border-gray-300'}`}
                        placeholder="Enter author's name"
                    />
                    {errors.author && <p className="text-xs text-red-600">{errors.author}</p>}
                </div>

                {/* Category & Status */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                        <label className="mb-2 block text-sm font-medium">Category</label>
                        <SearchDropDown
                            classes="!h-14 w-full !z-[9999]"
                            dropdownOptions={categories.slice(1).map((cat) => ({ label: cat }))}
                            initail={formData.category}
                            setSelectedStatus={(value: string) => {
                                setFormData((prev: any) => ({ ...prev, category: value }));
                                if (errors.category) setErrors((prev) => ({ ...prev, category: '' }));
                            }}
                            placeholder="Select category"
                        />
                        {errors.category && <p className="text-xs text-red-600">{errors.category}</p>}
                    </div>

                    <div>
                        <label className="mb-2 block text-sm font-medium">Status</label>
                        <SearchDropDown
                            classes="!h-14 w-full !z-[9999]"
                            dropdownOptions={statusOptions}
                            initail={statusOptions.find((option) => option.value === status)?.label || 'Select status'}
                            setSelectedStatus={(value: string) => {
                                const found = statusOptions.find((opt) => opt.label === value);
                                if (found) setStatus(found.value);
                            }}
                        />
                    </div>
                    {errors.status && <p className="text-xs text-red-600">{errors.status}</p>}
                </div>

                {/* Excerpt */}
                <div>
                    <label className="mb-2 block text-sm font-medium">Excerpt</label>
                    <textarea
                        name="excerpt"
                        value={formData.excerpt}
                        onChange={handleInputChange}
                        rows={3}
                        className={`w-full resize-none rounded-md border px-3 py-2 text-sm ${errors.excerpt ? 'border-red-500' : 'border-gray-300'}`}
                        placeholder="Enter a brief excerpt of the post"
                    />
                    {errors.excerpt && <p className="text-xs text-red-600">{errors.excerpt}</p>}
                </div>

                {/* Content + Live TOC */}
                <div>
                    <label className="mb-2 block text-sm font-medium">Content</label>
                    <div className="ck-editor-wrapper">
                        {!editorReady || !CKEditorPkg || !ClassicBuild ? (
                            <div className="rounded-md border border-dashed border-gray-300 p-6 text-sm text-gray-500">Loading editor…</div>
                        ) : (
                            <CKEditorPkg
                                editor={ClassicBuild}
                                data={editorContent}
                                config={{
                                    toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote', 'insertTable', 'undo', 'redo', 'imageUpload', 'mediaEmbed'],
                                    image: {
                                        // Enable the corner resize handles
                                        resizeUnit: '%', // or 'px'
                                        resizeOptions: [
                                            {
                                                name: 'resizeImage:original',
                                                label: 'Original',
                                                value: null,
                                            },
                                            {
                                                name: 'resizeImage:50',
                                                label: '50%',
                                                value: '50',
                                            },
                                            {
                                                name: 'resizeImage:75',
                                                label: '75%',
                                                value: '75',
                                            },
                                        ],
                                        toolbar: [
                                            'imageTextAlternative',
                                            'toggleImageCaption',
                                            'linkImage',
                                            '|',
                                            'imageStyle:inline',
                                            'imageStyle:block',
                                            'imageStyle:side',
                                            '|',
                                            'resizeImage:original',
                                            '',
                                        ],
                                    },
                                    // Base64 image adapter (client-only)
                                    extraPlugins: [
                                        function (editor: any) {
                                            const repo = editor.plugins.get('FileRepository');
                                            repo.createUploadAdapter = (loader: any) => {
                                                return {
                                                    upload: () =>
                                                        loader.file.then(
                                                            (file: any) =>
                                                                new Promise((resolve, reject) => {
                                                                    const reader = new window.FileReader();
                                                                    reader.readAsDataURL(file);
                                                                    reader.onload = () => resolve({ default: reader.result as string });
                                                                    reader.onerror = (error: any) => reject(error);
                                                                })
                                                        ),
                                                };
                                            };
                                        },
                                    ],
                                }}
                                onChange={(_: any, editor: any) => {
                                    const data = editor.getData();
                                    setEditorContent(data);
                                    setFormData((prev: any) => ({ ...prev, content: data }));
                                }}
                            />
                        )}
                    </div>
                    {errors.content && <p className="text-xs text-red-600">{errors.content}</p>}

                    {/* Live TOC preview */}
                    <div className="mt-4 rounded-md border border-gray-200 p-3">
                        <div className="mb-2 flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-800">Table of contents (live preview)</span>
                            <label className="flex items-center gap-2 text-xs text-gray-600">
                                <input type="checkbox" className="h-4 w-4" checked={insertTocOnSave} onChange={(e) => setInsertTocOnSave(e.target.checked)} />
                                Insert TOC at top on save
                            </label>
                        </div>
                        {liveTocItems.length ? (
                            <ul className="list-none space-y-1">
                                {liveTocItems.map((it, idx) => (
                                    <li key={idx} className={`text-sm ${it.level === 3 ? 'ml-4' : it.level === 4 ? 'ml-8' : ''}`} title={`H${it.level}`}>
                                        <span className="text-gray-700">{it.text || '—'}</span>
                                    </li>
                                ))}
                            </ul>
                        ) : (
                            <p className="text-xs text-gray-500">Add some headings (H2–H4) to see the TOC.</p>
                        )}
                    </div>
                </div>

                {/* Tags */}
                <div>
                    <label className="mb-2 block text-sm font-medium">Tags</label>
                    <input
                        type="text"
                        name="tags"
                        value={formData.tags}
                        onChange={handleInputChange}
                        placeholder="tag1, tag2, tag3"
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                    />
                </div>

                <div>
                    <label className="mt-2 inline-flex items-center">
                        <input
                            type="checkbox"
                            checked={formData.featureBlog}
                            onChange={(e) => setFormData((prev: any) => ({ ...prev, featureBlog: e.target.checked }))}
                            className="form-checkbox h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">Feature this blog post</span>
                    </label>
                </div>

                {/* Featured Image */}
                <div>
                    <label className="mb-2 block text-sm font-medium">Featured Image</label>
                    <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) setFormData((prev: any) => ({ ...prev, featuredImage: file }));
                        }}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                    />

                    {/* New file preview */}
                    {formData.featuredImage && typeof formData.featuredImage !== 'string' && (
                        <img src={URL.createObjectURL(formData.featuredImage)} alt="Preview" className="mt-2 h-32 rounded object-cover" />
                    )}

                    {/* Existing image */}
                    {formData.featuredImage && typeof formData.featuredImage === 'string' && <img src={formData.featuredImage} alt="Current Featured" className="mt-2 h-32 rounded object-cover" />}
                </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 border-t border-gray-200 px-6 py-4">
                <button onClick={onClose} className="rounded border bg-gray-100 px-4 py-2 text-sm text-[#636363]">
                    Cancel
                </button>
                <button onClick={handleSubmitPost} className="rounded bg-[#1D7EB6] px-4 py-2 text-sm text-white hover:bg-[#166da0]">
                    {mode === 'edit' ? 'Update Post' : 'Create Post'}
                </button>
            </div>
        </div>
    );
}
