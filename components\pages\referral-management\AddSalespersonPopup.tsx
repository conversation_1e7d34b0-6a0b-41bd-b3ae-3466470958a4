import API_ENDPOINTS from '@/app/lib/apiRoutes';
import React, { useEffect, useState } from 'react';

interface AddSalespersonPopupProps {
    isOpen: boolean;
    onClose: () => void;
    mode: string;
    initialData?: SalespersonFormData | null;
    setNotification: (val: { message: string; subMessage: string }) => void;
    onSubmit?: () => void;
}

export interface SalespersonFormData {
    id?: number;
    fullName: string;
    email: string;
    phone: string;
    referralId: string;
    commissionRate: number;
    status: 'Pending' | 'Active' | 'Inactive';
    notes?: string;
}

export default function AddSalespersonPopup({ isOpen, onClose, mode, initialData, onSubmit, setNotification }: AddSalespersonPopupProps) {
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState<SalespersonFormData>({
        fullName: '',
        email: '',
        phone: '',
        referralId: '',
        commissionRate: 5,
        status: 'Pending',
        notes: '',
    });

    useEffect(() => {
        if (mode === 'edit' && initialData) {
            setFormData(initialData);
        }
    }, [initialData, mode]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        try {
            const addedFormData = new FormData();
            addedFormData.append('fullName', formData.fullName);
            addedFormData.append('email', formData.email);
            addedFormData.append('phone', formData.phone);
            addedFormData.append('referralId', formData.referralId);
            addedFormData.append('commissionRate', String(formData.commissionRate));
            addedFormData.append('status', formData.status);
            if (formData.notes) {
                addedFormData.append('notes', formData.notes);
            }

            const endpoint = mode === 'edit' ? `${API_ENDPOINTS.SALESPERSONS}/${initialData?.id!}` : `${API_ENDPOINTS.SALESPERSONS}`;
            const method = mode === 'edit' ? 'PUT' : 'POST';

            const response = await fetch(endpoint, {
                method,
                body: addedFormData,
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`Failed to ${mode === 'edit' ? 'update' : 'create'} salesperson`);
            }

            setNotification({
                message: mode === 'edit' ? 'Edit Salesperson' : 'Success',
                subMessage: mode === 'edit' ? `Editing ${formData.fullName}` : 'New salesperson has been added successfully',
            });

            onSubmit?.();
            onClose();
        } catch (err: any) {
            console.error('Submission error:', err);
            setNotification({
                message: 'error',
                subMessage: `Error: ${err.message}`,
            });
        } finally {
            setLoading(false);
        }
    };

    const generateReferralId = () => {
        const prefix = 'REF';
        const randomNum = Math.floor(1000 + Math.random() * 9000);
        setFormData({ ...formData, referralId: `${prefix}${randomNum}` });
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50 ">
            <div className="md:w-[480px] rounded-lg bg-white p-4  max-h-[90vh] overflow-y-auto">
                <div className="mb-3 flex items-center justify-between">
                    <h3 className="text-base font-medium text-[#2d2d2e]">{mode === 'edit' ? 'Edit Salesperson' : 'Add New Salesperson'}</h3>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
                        <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                            />
                        </svg>
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-3">
                    {/* Full Name */}
                    <div>
                        <label className="block text-sm font-medium text-[#2d2d2e]">Full Name</label>
                        <input
                            type="text"
                            value={formData.fullName}
                            onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                            placeholder="Enter full name"
                            className="mt-1 h-10 w-full rounded-lg border border-[#e4e4e4] px-3 text-sm focus:border-[#1D7EB6] focus:outline-none"
                            required
                        />
                    </div>

                    {/* Email */}
                    <div>
                        <label className="block text-sm font-medium text-[#2d2d2e]">Email</label>
                        <input
                            type="email"
                            value={formData.email}
                            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                            placeholder="Enter email address"
                            className="mt-1 h-10 w-full rounded-lg border border-[#e4e4e4] px-3 text-sm focus:border-[#1D7EB6] focus:outline-none"
                            required
                        />
                    </div>

                    {/* Phone */}
                    <div>
                        <label className="block text-sm font-medium text-[#2d2d2e]">Phone (Optional)</label>
                        <input
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                            placeholder="Enter phone number"
                            className="mt-1 h-10 w-full rounded-lg border border-[#e4e4e4] px-3 text-sm focus:border-[#1D7EB6] focus:outline-none"
                        />
                    </div>

                    {/* Referral ID */}
                    <div>
                        <label className="block text-sm font-medium text-[#2d2d2e]">Referral ID</label>
                        <div className="mt-1 flex gap-2">
                            <input
                                type="text"
                                value={formData.referralId}
                                onChange={(e) => setFormData({ ...formData, referralId: e.target.value })}
                                placeholder="Enter referral ID"
                                className="h-10 w-full rounded-lg border border-[#e4e4e4] px-3 text-sm focus:border-[#1D7EB6] focus:outline-none"
                                required
                            />
                            <button type="button" onClick={generateReferralId} className="h-10 rounded-lg border border-[#e4e4e4] px-4 text-sm font-medium text-[#636363] hover:bg-gray-50">
                                Generate
                            </button>
                        </div>
                    </div>

                    {/* Commission Rate */}
                    <div>
                        <label className="block text-sm font-medium text-[#2d2d2e]">Commission Rate (%)</label>
                        <input
                            type="number"
                            value={formData.commissionRate}
                            onChange={(e) => setFormData({ ...formData, commissionRate: Number(e.target.value) })}
                            min="0"
                            max="100"
                            className="mt-1 h-10 w-full rounded-lg border border-[#e4e4e4] px-3 text-sm focus:border-[#1D7EB6] focus:outline-none"
                            required
                        />
                    </div>

                    {/* Status */}
                    <div>
                        <label className="block text-sm font-medium text-[#2d2d2e]">Status</label>
                        <select
                            value={formData.status}
                            onChange={(e) => setFormData({ ...formData, status: e.target.value as 'Pending' | 'Active' | 'Inactive' })}
                            className="mt-1 h-10 w-full rounded-lg border border-[#e4e4e4] px-3 text-sm focus:border-[#1D7EB6] focus:outline-none"
                        >
                            <option value="Pending">Pending</option>
                            <option value="Active">Active</option>
                            <option value="Inactive">Inactive</option>
                        </select>
                    </div>

                    {/* Notes */}
                    <div>
                        <label className="block text-sm font-medium text-[#2d2d2e]">Notes (Optional)</label>
                        <textarea
                            value={formData.notes}
                            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                            placeholder="Additional notes..."
                            rows={2}
                            className="mt-1 w-full rounded-lg border border-[#e4e4e4] px-3 py-2 text-sm focus:border-[#1D7EB6] focus:outline-none"
                        />
                    </div>

                    {/* Buttons */}
                    <div className="flex justify-end gap-3 pt-3">
                        <button type="button" disabled={loading} onClick={onClose} className="h-10 rounded-lg border border-[#e4e4e4] px-6 text-sm font-medium text-[#636363] hover:bg-gray-50">
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className={`h-10 rounded-lg px-6 text-sm font-medium text-white ${loading ? 'cursor-not-allowed bg-gray-400' : 'bg-[#1D7EB6] hover:bg-[#166da0]'}`}
                        >
                            {loading ? (mode === 'edit' ? 'Updating...' : 'Adding...') : mode === 'edit' ? 'Update Salesperson' : 'Add Salesperson'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
