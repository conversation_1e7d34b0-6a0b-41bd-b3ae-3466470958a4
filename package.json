{"name": "findanyagent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3082", "build": "next build", "start": "next start -p 3082", "lint": "next lint"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-react": "^11.0.0", "@emotion/react": "^11.10.6", "@fullcalendar/core": "^6.1.4", "@fullcalendar/daygrid": "^6.1.4", "@fullcalendar/interaction": "^6.1.1", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.4", "@fullcalendar/timegrid": "^6.1.1", "@headlessui/react": "^1.7.8", "@hookform/resolvers": "^4.1.0", "@mantine/core": "^5.10.5", "@mantine/hooks": "^5.10.5", "@reduxjs/toolkit": "^1.9.1", "@tinymce/tinymce-react": "^6.3.0", "@tippyjs/react": "^4.2.6", "@types/axios": "^0.9.36", "@types/node": "18.11.18", "@x1mrdonut1x/nouislider-react": "^3.4.3", "apexcharts": "^5.3.2", "axios": "^1.9.0", "crypto-js": "^4.2.0", "easymde": "^2.18.0", "eslint": "8.32.0", "eslint-config-next": "13.1.2", "formik": "^2.4.6", "framer-motion": "^12.4.10", "highlight.js": "^11.7.0", "i18next": "^22.4.10", "js-cookie": "^3.0.5", "lucide-react": "^0.511.0", "mantine-datatable": "^1.7.35", "next": "14.0.3", "next-auth": "^4.24.11", "ni18n": "^1.0.5", "quill": "^2.0.3", "quill-better-table": "^1.2.10", "quill-table": "^1.0.0", "react": "18.2.0", "react-animate-height": "^3.1.0", "react-apexcharts": "^1.4.0", "react-click-away-listener": "^2.2.2", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.4.1", "react-datepicker": "^8.1.0", "react-dom": "18.2.0", "react-flatpickr": "^3.10.13", "react-i18next": "^12.1.5", "react-icons": "^5.5.0", "react-images-uploading": "^3.1.7", "react-perfect-scrollbar": "^1.5.8", "react-popper": "^2.3.0", "react-quill": "^2.0.0", "react-redux": "^8.1.3", "react-select": "^5.7.0", "react-simplemde-editor": "^5.2.0", "react-sortablejs": "^6.1.4", "react-text-mask": "^5.5.0", "sortablejs": "^1.15.0", "sweetalert2": "^11.7.1", "sweetalert2-react-content": "^5.0.7", "swiper": "^11.1.15", "tailwind-merge": "^3.0.2", "tinymce": "^8.0.2", "typescript": "4.9.4", "universal-cookie": "^6.1.1", "yet-another-react-lightbox": "^3.15.6", "yup": "^0.32.11", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.8", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.191", "@types/react": "^19.1.10", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^19.1.7", "@types/react-flatpickr": "^3.8.8", "@types/react-redux": "^7.1.32", "@types/react-text-mask": "^5.4.11", "@types/sortablejs": "^1.15.0", "autoprefixer": "^10.4.13", "postcss": "^8.4.21", "prettier": "^2.8.0", "prettier-plugin-tailwindcss": "^0.2.0", "tailwindcss": "^3.3.2"}}