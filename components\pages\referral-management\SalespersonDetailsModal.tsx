"use client"

import { X, CheckCircle, Mail, Phone, Calendar, Copy } from "lucide-react"

interface Salesperson {
  id: number
  full_name: string
  email: string
  phone: string
  statusId: number
  referral_id: string
  commission_rate: number
  status_name: string // e.g., "active"
  created_at: string // Join Date
  last_activity: string // Last Activity
  referrals: number
  totalCommission: number
  paidCommission: number
  pendingCommission: number
  notes?: string
}

interface SalespersonDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  person: Salesperson | null
}

export default function SalespersonDetailsModal({ isOpen, onClose, person }: SalespersonDetailsModalProps) {
  if (!isOpen || !person) return null

  return (
    <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50  ">
      <div className="relative w-full max-w-xl overflow-hidden rounded-lg bg-white shadow-lg max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 px-6 pb-4 pt-6">
          <h2 className="text-lg font-semibold text-[#2d2d2e]">Salesperson Details</h2>
          <button
            onClick={onClose}
            className="rounded-md p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
            aria-label="Close"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content Area */}
        <div className="space-y-6  p-2 pb-5">
          {/* Basic Information Card */}
          <div className="rounded-lg bg-white  border px-4 py-2 border-gray-300 shadow ">
            <h3 className="pb-4 text-base font-semibold text-[#2d2d2e]">Basic Information</h3>
            <div className="grid grid-cols-2 gap-x-8 gap-y-4 text-sm text-gray-700">
              <div className="col-span-1">
                <div className="text-xs text-gray-500">Name</div>
                <div className="font-medium text-[#2d2d2e]">{person.full_name}</div>
              </div>
              <div className="col-span-1">
                <div className="text-xs text-gray-500">Status</div>
                <span className="flex w-fit items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-700">
                  <CheckCircle className="mr-1 h-3 w-3 fill-green-500 text-green-500" />
                  {person.status_name}
                </span>
              </div>
              <div className="col-span-1">
                <div className="text-xs text-gray-500">Email</div>
                <div className="flex items-center gap-1">
                  <Mail className="h-4 w-4 text-gray-500" />
                  {person.email}
                </div>
              </div>
              <div className="col-span-1">
                <div className="text-xs text-gray-500">Phone</div>
                <div className="flex items-center gap-1">
                  <Phone className="h-4 w-4 text-gray-500" />
                  {person.phone || "—"}
                </div>
              </div>
              <div className="col-span-1">
                <div className="text-xs text-gray-500">Join Date</div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  {new Date(person.created_at).toLocaleDateString()}
                </div>
              </div>
              {/* <div className="col-span-1">
                <div className="text-xs text-gray-500">Last Activity</div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  {new Date(person.last_activity).toLocaleDateString()}
                </div>
              </div> */}
            </div>
          </div>

          {/* Referral Information Card */}
          <div className="rounded-lg bg-white border px-4 py-2 border-gray-300 shadow " >
            <h3 className="pb-4 text-base font-semibold text-[#2d2d2e]">Referral Information</h3>
            <div className="grid gap-y-4 text-sm text-gray-700">
              <div>
                <div className="text-xs text-gray-500">Referral ID</div>
                <div className="flex items-center gap-2">
                  <span className="rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
                    {person.referral_id}
                  </span>
                  <button
                    className="flex h-6 w-6 items-center justify-center rounded-md text-gray-500 hover:bg-gray-100 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300"
                    aria-label="Copy Referral ID"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Commission Rate</div>
                <span className="rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
                  {person.commission_rate}%
                </span>
              </div>
            </div>
          </div>

          {/* Performance Statistics Card */}
          <div className="rounded-lg bg-white border px-4 py-2 border-gray-300 shadow ">
            <h3 className="pb-4 text-base font-semibold text-[#2d2d2e]">Performance Statistics</h3>
            <div className="grid grid-cols-2 gap-4 text-center md:grid-cols-4">
              <div>
                <div className="text-2xl font-bold text-[#2d2d2e]">{person.referrals}</div>
                <div className="text-xs text-gray-500">Total Referrals</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-500">AED {person.totalCommission.toLocaleString()}</div>
                <div className="text-xs text-gray-500">Total Commission</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-500">AED {person.paidCommission.toLocaleString()}</div>
                <div className="text-xs text-gray-500">Paid Commission</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-500">AED {person.pendingCommission.toLocaleString()}</div>
                <div className="text-xs text-gray-500">Pending Commission</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
