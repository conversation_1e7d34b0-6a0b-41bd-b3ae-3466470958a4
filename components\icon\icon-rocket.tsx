import { FC } from 'react';

interface IconArchiveProps {
    className?: string;
}

const IconRocket: FC<IconArchiveProps> = ({ className }) => {
    return (
        <svg width="66" height="65" viewBox="0 0 66 65" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
            <path d="M25.0116 50.3773C23.4744 52.8753 20.4295 56.6296 15.345 57.8268L16.5127 53.9837C16.5127 53.9837 12.0045 56.9842 7.67383 57.8268C8.51635 53.4812 11.5169 48.9879 11.5169 48.9879L7.67383 50.1556C8.87098 45.0711 12.6253 42.0262 15.1233 40.489L25.7668 38.254L25.0116 50.3773Z" fill="#FFAE4B" />
            <path d="M41.8268 60.5982C36.2732 66.1518 27.3813 66.3277 21.6173 61.1283C23.8926 60.8872 26.3455 59.0773 26.6887 55.8925C26.8317 54.5697 26.6092 53.0087 25.8585 51.2241L14.2754 39.641C12.4909 38.8904 10.9299 38.6679 9.60705 38.8109C6.42224 39.154 4.6124 41.6068 4.3713 43.8821C-0.828005 38.1182 -0.652337 29.2265 4.90134 23.6727C8.18538 20.3886 12.6395 18.9857 16.9229 19.4642C18.8869 19.6839 20.8144 20.2971 22.5774 21.3085L44.1908 42.9219C45.2023 44.6849 45.8165 46.6133 46.0351 48.5765C46.5139 52.8598 45.1107 57.3142 41.8268 60.5982Z" fill="#8080D2" />
            <path d="M46.0356 48.5765C40.2828 52.4186 33.7037 54.8415 26.689 55.8922C26.832 54.5694 26.6095 53.0084 25.8589 51.2239L14.2758 39.6407C12.4913 38.8901 10.9303 38.6676 9.60742 38.8106C10.6581 31.7959 13.0812 25.2167 16.9231 19.4641C18.8871 19.6838 20.8146 20.297 22.5776 21.3083L44.1911 42.9217C45.2027 44.685 45.8169 46.6133 46.0356 48.5765Z" fill="#4B4BC0" />
            <path d="M25.8584 51.2244C34.8742 49.1537 43.4365 44.6081 50.46 37.5845C60.7755 27.2691 65.748 13.6349 65.3809 0.118941C51.8649 -0.248216 38.2307 4.72416 27.9153 15.0396C20.8916 22.0633 16.3461 30.6255 14.2754 39.6413L25.8584 51.2244Z" fill="#E3E8EC" />
            <path d="M33.7734 20.8984C26.7503 27.9216 21.1603 35.441 17.2854 42.6511L14.2754 39.6411C16.3449 30.6259 20.8918 22.0632 27.915 15.0401C38.2303 4.72447 51.8645 -0.24765 65.3806 0.11976C55.4718 3.35975 44.0889 10.583 33.7734 20.8984Z" fill="#D5D8DB" />
            <path d="M62.367 18.8745C59.3467 17.0601 56.3516 14.7324 53.5602 11.9412C52.2123 10.5933 50.9735 9.19932 49.8481 7.77853C48.6412 6.25383 47.5634 4.6975 46.623 3.13446C52.6367 0.961002 58.9775 -0.0439027 65.293 0.116958C65.3229 0.118856 65.3529 0.118856 65.3819 0.119742L65.381 0.120628C65.5528 6.46418 64.5498 12.8329 62.367 18.8745Z" fill="#8080D2" />
            <path d="M63.4014 43.634C63.4004 43.633 63.4014 43.6318 63.4004 43.6308L63.4014 43.634Z" fill="#E30E08" />
            <path d="M65.3796 0.119803L65.3788 0.120689C60.4608 1.72765 55.1792 4.31699 49.8462 7.77859C48.6393 6.2539 47.5615 4.69756 46.6211 3.13452C52.6346 0.961063 58.9755 -0.0438417 65.2909 0.117019C65.3208 0.118917 65.3507 0.118917 65.3796 0.119803Z" fill="#4B4BC0" />
            <path d="M42.7262 32.2987C47.9868 32.2987 52.2513 28.0342 52.2513 22.7737C52.2513 17.5131 47.9868 13.2486 42.7262 13.2486C37.4657 13.2486 33.2012 17.5131 33.2012 22.7737C33.2012 28.0342 37.4657 32.2987 42.7262 32.2987Z" fill="#4B4BC0" />
            <path d="M42.7268 29.1879C46.2693 29.1879 49.1411 26.3162 49.1411 22.7736C49.1411 19.2311 46.2693 16.3593 42.7268 16.3593C39.1843 16.3593 36.3125 19.2311 36.3125 22.7736C36.3125 26.3162 39.1843 29.1879 42.7268 29.1879Z" fill="#C2C2FF" />
        </svg>
    );
};

export default IconRocket;



