'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React from 'react';
import { useRouter } from 'next/navigation';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
 
import BlogTable from './BlogsSubsciberTable';
import { CreateIcon } from '@/components/icon/Icon';


const BlogScriberTableLayout = () => {
    const { push } = useRouter();

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Blog Subscribers Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Blog Subscribers' }]}
               
            />
            
            <div className="px-4">
                <BlogTable/>

            </div>
        </DefaultPageLayout>
    );
};

export default BlogScriberTableLayout;
 
