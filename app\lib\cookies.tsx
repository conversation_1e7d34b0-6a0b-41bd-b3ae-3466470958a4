import Cookies from 'js-cookie';
import CryptoJs from 'crypto-js';

const _key = '7d7cd92a9c3055f30f8943b5092abb8e';

export function storeCookies(value: any, token: any) {
    const encryptedToken = CryptoJs.AES.encrypt(token, _key).toString();
    Cookies.set(value, encryptedToken, {
        expires: 7,
    });
}

export function getCookies(value: string): string {
    const encryptedToken = Cookies?.get(value);
    if (encryptedToken) {
        const decryptedToken = CryptoJs.AES.decrypt(encryptedToken, _key);
        const token = decryptedToken?.toString(CryptoJs.enc.Utf8) ?? '';
        if (isSessionExpired(token)) {
            // clearAllCookies()
            // return '';
        }
        return token;
    }
    return '';
}

const cookiesToClear = [
  "email",
  "firstName",
  "middleName",
  "lastName",
  "localName",
  "phone",
  "designation",
  "profileImage",
  "username",
  "adminUserId",
  "role",
];

export function clearAllCookies(): void {
    cookiesToClear?.forEach((cookieName) => {
        if (Cookies?.get(cookieName)) {
            Cookies?.remove(cookieName, {
                path: '/',
                maxAge: 0,
            });
        }
    });
}

// Enhanced debugging for getAndDecryptCookie
export const getAndDecryptCookie = (cookieName: string): string => {
    const encryptedValue = Cookies?.get(cookieName);
    console.log(`Raw cookie value for ${cookieName}:`, encryptedValue); // Debugging raw cookie value

    if (encryptedValue) {
        try {
            const decryptedToken = CryptoJs.AES.decrypt(encryptedValue, _key);
            const token = decryptedToken?.toString(CryptoJs.enc.Utf8) ?? '';
            console.log(`Decrypted token for ${cookieName}:`, token); // Debugging decrypted token

            // Check if the token is expired
            if (isSessionExpired(token)) {
                console.warn(`Token for ${cookieName} is expired.`);
                return '';
            }

            return token;
        } catch (error) {
            console.error(`Error decrypting cookie ${cookieName}:`, error);
        }
    } else {
        console.warn(`Cookie ${cookieName} not found. Please log in.`);
        // Instead of redirecting, log a warning or trigger an event
        return '';
    }

    return '';
};

export function isSessionExpired(token: any) {
    try {
        const decryptedToken = CryptoJs.AES.decrypt(token, _key).toString(CryptoJs.enc.Utf8);
        const tokenPayload = JSON.parse(decryptedToken);
        const currentTime = Math.floor(Date.now() / 1000);

        return tokenPayload.exp < currentTime;
    } catch (error) {
        return true; // If there's an error in decryption, consider the session expired
    }
}
