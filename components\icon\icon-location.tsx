

import { FC } from 'react';

interface IconLockDotsProps {
    className?: string;
    fill?: boolean;
}

const LocationIcon: FC<IconLockDotsProps> = ({ className, fill = false }) => {
    return (
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
            <g id="weui:location-filled">
                <path id="Vector" fill-rule="evenodd" clip-rule="evenodd" d="M5.81578 10.5889C5.81578 10.5889 2.52771 7.81969 2.52771 5.09488C2.52771 4.1342 2.90934 3.21288 3.58863 2.53358C4.26793 1.85428 5.18926 1.47266 6.14993 1.47266C7.1106 1.47266 8.03193 1.85428 8.71123 2.53358C9.39052 3.21288 9.77215 4.1342 9.77215 5.09488C9.77215 7.81969 6.48408 10.5889 6.48408 10.5889C6.30116 10.7573 6.00006 10.7555 5.81578 10.5889ZM6.14993 6.6796C6.35804 6.6796 6.56411 6.63861 6.75638 6.55897C6.94864 6.47933 7.12334 6.3626 7.2705 6.21544C7.41765 6.06829 7.53438 5.89359 7.61402 5.70132C7.69366 5.50906 7.73465 5.30298 7.73465 5.09488C7.73465 4.88677 7.69366 4.6807 7.61402 4.48843C7.53438 4.29616 7.41765 4.12146 7.2705 3.97431C7.12334 3.82715 6.94864 3.71042 6.75638 3.63078C6.56411 3.55114 6.35804 3.51015 6.14993 3.51015C5.72964 3.51015 5.32656 3.67712 5.02936 3.97431C4.73217 4.2715 4.56521 4.67458 4.56521 5.09488C4.56521 5.51517 4.73217 5.91825 5.02936 6.21544C5.32656 6.51264 5.72964 6.6796 6.14993 6.6796Z" fill="#FFF3F3" />
            </g>
        </svg>
    );
};
export default LocationIcon;
