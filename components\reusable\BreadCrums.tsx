import React from "react";
import Link from "next/link"; 

const BreadCrums = ({ mainHeading, breadcrumbs , ButonComponent }:any) => {
  return (
    <div>
      <div className={` ${ButonComponent ? " pt-10 flex  items-center justify-between ":" pt-6 flex-col  items-start justify-start "}  font-inter h-[88px] pt-10  pb-6 w-full    justify-between gap-2.5 border-b border-[#e4e4e4] bg-neutral-100 md:px-5 px-4 py-2.5"`}>
        <div className="flex flex-col items-start justify-center gap-0.5 self-stretch">
          <div className="font-['Golos Text'] md:text-[22px] text-[18px] font-semibold text-[#2d2d2e]">
            {mainHeading}
          </div>
          <div className="flex items-center md:text-sm text-xs text-[#636363] pt-2">
            {breadcrumbs.map((breadcrumb:any, index:any) => (
              <React.Fragment key={index}>
                {breadcrumb.url ? (
                  <Link href={breadcrumb.url} className="hover:underline">
                    {breadcrumb.text}
                  </Link>
                ) : (
                  <span className={`font-medium ${breadcrumb.no ? "text-[#636363]" :" text-[#993333]"}`}>{breadcrumb.text}</span>
                )}
                {index < breadcrumbs.length - 1 && <span className="mx-1">/</span>}
              </React.Fragment>
            ))}
          </div>
        </div>
        {ButonComponent ?  <div className="md:hidden flex"  >

          {ButonComponent}
        </div> :null}
      </div>
    </div>
  );
};

export default BreadCrums;
