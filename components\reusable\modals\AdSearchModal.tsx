"use client";

import IconCopy from "@/components/icon/icon-copy";
import { CalendarIcon, FileUploadIcon } from "@/components/icon/Icon";
import ReusebleButton from "../Button";
import IconPaper from "@/components/icon/icon-paper";
import { ButtonBorder } from "../ButtonBorder";
import DateInput from "../DateInput";
import LabelInputField from "../LabelInputField";
import FileUpload from "../FileUploading";

export default function AdSearchModal({ onClose }: { onClose: () => void }) {

    const textToCopy = "FAA218239713";

    const handleCopy = () => {
        navigator.clipboard.writeText(textToCopy);
    };


    return (
        <div className="max-w-md font-inter">
            <div className="flex items-center justify-between border-b pb-3">
                <div className="md:w-10/12 text-black">
                    <h2 className="text-xl font-semibold">
                        Advertising <span className="text-redMain">on Search Results</span>
                    </h2>
                    <p className="text-gray-600 mt-2 text-xs">
                        Showcase your brand with high-impact banner advertisements.
                    </p>
                </div>

                <div className="md:w-2/12 px-4">
                    {/* <IconPhotos /> */}
                </div>
            </div>

            <div className="mt-4">
                <p className="text-xs pb-1">Campaign ID:</p>
                <div className="flex items-center p-2 ">
                    <span className="text-gray-700">{textToCopy}</span>
                    <button onClick={handleCopy} className="ml-2"><IconCopy className="w-5 h-5" /></button>
                </div>
            </div>

            <div className="flex md:flex-row flex-col md:gap-0 gap-2 items-center">
                <div className="mt-2 md:w-1/2 w-full">
                    <DateInput placeholder="Ad Start Date*" icon={<CalendarIcon />} />
                </div>

                <div className="mt-2 md:w-1/2 md:px-4 w-full">

                    <label className="block font-normal text-sm text-gray-700">Select Ad Duration</label>
                    <div className="flex gap-4 mt-2">
                        <label className="flex items-center font-normal">
                            <input type="radio" name="duration" className="mr-2" /> 15 Days
                        </label>
                        <label className="flex items-center font-normal">
                            <input type="radio" name="duration" className="mr-2" /> 30 Days
                        </label>
                    </div>
                </div>
            </div>

            <div className="mt-4">
                <LabelInputField
                    label="Add Location(s)*"
                    id="location-input"
                />
            </div>

            <div className="mt-4">
                <LabelInputField
                    label="Keywords"
                    id="keywords-input"
                />
            </div>


            <div className="py-2 pt-4">
                <p className="text-sm font-bold pb-1 text-black" >Upload Search Banner</p>
                <p className="text-xs text-gray-600 pb-1">Upload your main homepage banner in JPG or PNG format, with a maximum file size of 1 MB and dimensions of 594 x 220 pixels.</p>
                <div className="  mt-2">
                    <FileUpload />
                </div>
            </div>




            <div className=" bg-lightGray p-3 rounded-md flex gap-2">
                <IconPaper />
                <p>
                    Based on your selection you will be charged <span className="text-red-500 font-bold">AED 499</span> for 15 Days.
                </p>
            </div>

            <div className="mt-4 flex items-start">
                <input type="checkbox" className="mr-2 mt-1" />
                <p className="text-sm text-gray-700 pb-2">
                    By checking this box, you agree to the ad payment
                    <a href="#" className="text-blue-500 underline"> terms and conditions</a>.
                </p>
            </div>

            <div className="mt-6 flex justify-end gap-3">
                <button onClick={onClose} className="text-blue-500">Cancel</button>

                <div>
                    <ButtonBorder value="Continue to Pay" className="!bg-blueMain !text-white" />
                </div>
            </div>
        </div>
    );
}