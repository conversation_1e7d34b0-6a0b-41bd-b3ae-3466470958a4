'use client'; // Mark as client component to use hooks

import React, { useState, useEffect, useRef } from 'react';

interface Option {
    name: string;
    initail?: string;
    dropdownOptions?: Option[];
}

const ProfileDropdown = ({ classes, initail, dropdownOptions, status, onClick, disabled }: any) => {
    // Main dropdown options

    const [isOpen, setIsOpen] = useState(false);
    const [selected, setSelected] = useState('');
    const dropdownRef = useRef<HTMLDivElement>(null);

    const toggleDropdown = () => setIsOpen(!isOpen);

    const selectOption = (option: any) => {
        setSelected(option?.name);
        setIsOpen(false);
        onClick(option);
    };

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    useEffect(() => {
        if (initail && dropdownOptions.length) {
            const option = dropdownOptions?.find((option: any) => option.name === initail);
            if (option) {
                onClick(option);
                setSelected(option?.name);
            }
        }
    }, [initail, dropdownOptions]);

    return (
        <div ref={dropdownRef} className={`inline- relative h-full w-full rounded-lg ${classes}`}>
            <div
                onClick={toggleDropdown}
                className={`text-grayText flex h-full max-h-10 cursor-pointer items-center justify-between gap-2 rounded-md bg-transparent font-inter ${
                    isOpen ? '!border-lightBlue  focus:outline-none' : ''
                }`}
            >
                {status && selected && <div className="e absolute left-3 top-3.5 -ml-2   -mt-2 rounded-t-lg  py-1 text-xs  font-normal">Selected Status</div>}
                <p
                    className={`${selected ? 'text-base font-normal capitalize text-black ' : ' text-base font-normal text-neutral-400 '} 
                
                ${status && selected ? 'pt-5' : ''}
                `}
                >
                    {selected ? selected : 'Select Location'}
                </p>

                <svg
                    className={`h-4 w-4 transform text-gray-600 transition-transform ${isOpen ? 'rotate-180' : 'rotate-0'}`}
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
            </div>
            {isOpen && (
                <div className="border-borderColor shadow-shadowcustom absolute top-3 z-30 mt-2 w-full rounded border bg-white py-2">
                    <ul className="scrollbar-main-22 max-h-40 overflow-y-scroll  py-2">
                        {dropdownOptions.map((option: Option, index: number) => (
                            <li key={index} className="cursor-pointer px-4 py-2  text-lg capitalize hover:bg-gray-200 hover:text-[#1D7EB6]" onClick={() => !disabled && selectOption(option)}>
                                {option.name}
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default ProfileDropdown;
