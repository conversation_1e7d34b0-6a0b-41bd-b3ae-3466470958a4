'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React from 'react';
import { useRouter } from 'next/navigation';
import SubscriptionInfo from '../components/SubscribtionInfo';
import CardInfo from '../components/CardInfo';
import AccountProfileInfo from './AccountProfileInfo';

const AccountDetails = () => {
    const { push } = useRouter();

    return (
        <>
            <DefaultPageLayout>
                <BreadCrums mainHeading="Verification Details" breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Verification Details' }]} />
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-3 font-inter pt-10 pe-2">
                <div className="col-span-12 md:col-span-9 px-4">
                   <AccountProfileInfo />
                   </div>
                   <div className="col-span-12 md:col-span-3 max-lg:px-4 flex flex-col gap-5">
                        <SubscriptionInfo />
                        <CardInfo />
                    </div>
                </div>
            </DefaultPageLayout>
        </>
    );
};

export default AccountDetails;
