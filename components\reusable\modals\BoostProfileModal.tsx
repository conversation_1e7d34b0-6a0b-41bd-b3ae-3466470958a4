"use client";
import IconRocket from "@/components/icon/icon-rocket";
import IconCopy from "@/components/icon/icon-copy";
import InputField from "../InputField";
import { CrossIcon } from "@/components/icon/Icon";
import IconPaper from "@/components/icon/icon-paper";
import { Button } from "./Button copy";
import DateInput from "../DateInput";

import { useState } from "react";
import { AgentDropdown } from "../AgentDropdown";
import { CalendarIcon } from "@/components/icon/Icon";
import LabelInputField from "../LabelInputField";

interface Agent {
    label: string;
    value: string;
}

export default function BoostProfileModal({ onClose }: { onClose: () => void }) {

    const textToCopy = "FAA218239713";

    const handleCopy = () => {
        navigator.clipboard.writeText(textToCopy);
    };

    const agents: Agent[] = [
        { label: "Own Profile", value: "own-profile" },
        { label: "Agent 1", value: "agent-1" },
        { label: "Agent 2", value: "agent-2" },
        { label: "Agent 3", value: "agent-3" },
        { label: "Agent 4", value: "agent-4" },
        { label: "Agent 5", value: "agent-5" },
        { label: "Agent 6", value: "agent-6" },
        { label: "Agent 7", value: "agent-7" },
        { label: "Agent 8", value: "agent-8" },
        { label: "Agent 9", value: "agent-9" },
        { label: "Agent 10", value: "agent-10" },
    ];



    const [selectedAgents, setSelectedAgents] = useState<Agent[]>([]);

    const handleSelect = (selected: Agent[]) => {
        setSelectedAgents(selected);
    };

    const handleRemove = (value: string) => {
        setSelectedAgents((prev) => prev.filter((agent) => agent.value !== value));
    };


    return (
        <div className="max-w-md font-inter">
            <div className="flex items-center justify-between border-b pb-3">
                <div className="md:w-10/12 text-black">
                    <h2 className="text-xl font-semibold">
                        Boost Your <span className="text-redMain">Profile</span>
                    </h2>
                    <p className="text-gray-600 mt-2 text-xs">
                        You have selected the <strong>Profile Boost Package</strong> to enhance your
                        profile and increase visibility among 1,566 other agents.
                    </p>
                </div>

                <div className="md:w-2/12 px-4">
                    <IconRocket className="" />
                </div>
            </div>

            <div className="mt-4">
                <p className="text-xs pb-1">Campaign ID:</p>
                <div className="flex items-center p-2 ">
                    <span className="text-gray-700">{textToCopy}</span>
                    <button onClick={handleCopy} className="ml-2"><IconCopy className="w-5 h-5" /></button>
                </div>
            </div>

            <div className="flex md:flex-row flex-col md:gap-0 gap-2 items-center">
                <div className="mt-2 md:w-1/2 w-full">
                    <DateInput placeholder="Ad Start Date*" icon={<CalendarIcon/>}/>
                </div>

                <div className="mt-2 md:w-1/2 md:px-4 w-full">

                    <label className="block font-normal text-sm text-grayMain">Select Ad Duration</label>
                    <div className="flex gap-4 mt-2">
                        <label className="flex items-center font-normal">
                            <input type="radio" name="duration" className="mr-2" /> 15 Days
                        </label>
                        <label className="flex items-center font-normal">
                            <input type="radio" name="duration" className="mr-2" /> 30 Days
                        </label>
                    </div>
                </div>
            </div>


            <div className="mt-4 md:w-1/2">
                <AgentDropdown
                    label="Select Profile(s)"
                    data={agents}
                    onHandleClick={handleSelect}
                />
            </div>

            <div className={`${selectedAgents.length > 0 ? "mt-4" : ""}`}>
                <div className="flex flex-wrap gap-2">
                    {selectedAgents.map((agent) => (
                        <span
                            key={agent.value}
                            className="bg-lightGray border-lineColor border text-lineColor border-opacity-30 text-xs rounded px-2 py-1 flex gap-1 items-center"
                        >
                            {agent.label}
                            <div onClick={() => handleRemove(agent.value)}>
                                <CrossIcon/>
                            </div>
                        </span>
                    ))}
                </div>
            </div>

            <div className="mt-4">
                <LabelInputField
                    label="Add Location(s)*"
                    id="location-input"
                />
            </div>

            <div className="mt-4">
                <LabelInputField
                    label="Keywords"
                    id="keywords-input"
                />
            </div>

            <div className="mt-4 bg-lightGray p-3 rounded-md flex gap-2">
                <IconPaper />
                <p>
                    Based on your selection, you will be charged <span className="text-red-500 font-bold">AED 1500</span> for 15 Days.
                </p>
            </div>

            <div className="mt-4 flex items-start">
                <input type="checkbox" className="mr-2 mt-1" />
                <p className="text-sm text-gray-700 pb-2">
                    By checking this box, you agree to the ad payment
                    <a href="#" className="text-blue-500 underline"> terms and conditions</a>.
                </p>
            </div>

            <div className="mt-6 flex justify-end gap-3">
                <button onClick={onClose} className="text-blue-500">Cancel</button>
                <div>
                    <Button value="Continue to Pay" className="!bg-blueMain !text-white" />
                </div>
            </div>
        </div>
    );
}