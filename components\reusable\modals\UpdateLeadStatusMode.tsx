'use client';
 
import DuplicateIcons from '@/../public/assets/images/UpdateLead.svg'; 
import Image from 'next/image'; 
import { CloseIconModel  } from '@/components/icon/Icon';
import Dropdown from '@/components/dropdown';
import SearchDropDown from '../SearchDropDown';

const UpdateLeadStatusModel = ({ onClose, onSuccessfulDelete }: any) => { 
    const handleDelete = ( ) => {
        onClose(true);
      
    };
    const dropdown = [{ label: 'Null' }, { label: 'Converted' }, { label: 'Not Converted' }, { label: 'In Progress' }];
  
    return (
        <>
            <div className=" w-full">
                <div className=" relative flex items-center justify-between ">
                    <div className=" relative flex items-start justify-start "></div>
                    <div className=" relative flex items-end justify-end ">
                        <span className="cursor-pointer    " onClick={handleDelete}>
                            <CloseIconModel />
                        </span>
                    </div>
                </div>
                <div className="min-h-[500px] flex flex-col justify-between">
                    <div className=" ">
                        <Image src={DuplicateIcons} alt="Success" width={200} height={200} className="m-auto rounded-full" />
                        <div className=" py-2 text-center">
                            <p className="font-inter text-[26px] font-semibold  text-[#993333]">Update Lead Status</p>
                            <p className="justify-start  self-stretch pt-5 text-center font-inter text-base font-normal leading-normal text-[#636363]">
                            Please update the current status of this lead for accurate customer record.
                            </p>
                        </div>
                        <div className='flex justify-center items-center'>
                        <div className="  w-full px-6 pb-10 ">
                            <SearchDropDown classes="!h-14 w-full  " initail={'Select Lead Type'} dropdownOptions={dropdown} />
                        </div>
                        </div> 
                        <div className="grid grid-cols-2 gap-4 max-md:grid-cols-1 pt-20">
                            <button onClick={handleDelete} type="submit" className="flex items-center justify-center  px-6 py-4 text-lg font-medium text-[#1D7EB6]">
                                Cancel
                            </button>
                            <button onClick={onSuccessfulDelete} className= "flex items-center justify-center rounded-lg bg-[#1D7EB6] px-6 py-4 text-lg font-medium !text-white"
                            
                            >
                                Update
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default UpdateLeadStatusModel;
 
