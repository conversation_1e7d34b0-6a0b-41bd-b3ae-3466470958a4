'use client';

import React from 'react';
import { useState, useRef, useEffect } from 'react';
import AddSalespersonPopup from './AddSalespersonPopup';
import SalespersonsTab from './SalespersonsTab';
import OverviewTab from './overview/OverviewTab';
import ReferralSection from './referral-section/Referral-Section';
import PaymentSection from './payments/Payment';

export default function ReferralManagementData() {
    const [activeTab, setActiveTab] = useState('overview');
    const [notification, setNotification] = useState<{ message: string; subMessage: string } | null>(null);
    const [isAddSalespersonOpen, setIsAddSalespersonOpen] = useState(false);

    // Auto-hide notification after 3 seconds
    useEffect(() => {
        if (notification) {
            const timer = setTimeout(() => {
                setNotification(null);
            }, 3000);
            return () => clearTimeout(timer);
        }
    }, [notification]);

    return (
        <div className="min-h-screen bg-[#f8f9fa] p-4">
     
            <div className="mb-8 flex items-center justify-end"> 
                    <button
                        onClick={() => setIsAddSalespersonOpen(true)}
                        className="flex items-center gap-2 rounded-md border border-[#1D7EB6] bg-[#1D7EB6] px-4 py-3 text-sm font-medium text-white transition hover:bg-[#166da0]"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Add Salesperson
                    </button>
                
            </div>

            {/* Navigation Tabs */}
            <div className="mb-8 border-b border-[#e4e4e4] bg-[#F1F5F9] shadow-sm rounded-md">
                <nav className="-mb-px grid md:grid-cols-4 grid-cols-2 gap-4 p-2">
                    <button
                        onClick={() => setActiveTab('overview')}
                        className={` py-2   px-1 text-center rounded text-sm font-medium ${
                            activeTab === 'overview' ? '  text-black bg-white shadow-md' : '  text-gray-400 '
                        }`}
                    >
                        Overview
                    </button>
                    <button
                        onClick={() => setActiveTab('salespersons')}
                        className={` px-1 py-2 text-center rounded text-sm font-medium ${
                            activeTab === 'salespersons' ? '  text-black bg-white shadow-md' : '  text-gray-400 '
                        }`}
                    >
                        Salespersons
                    </button>
                    <button
                        onClick={() => setActiveTab('referrals')}
                        className={` px-1 text-center  py-2  rounded text-sm font-medium ${
                            activeTab === 'referrals' ? '  text-black bg-white shadow-md' : '  text-gray-400 '
                        }`}
                    >
                        Referrals
                    </button>
                    <button
                        onClick={() => setActiveTab('payments')}
                        className={` px-1 text-center  py-2  rounded text-sm font-medium ${
                            activeTab === 'payments' ? '  text-black bg-white shadow-md' : '  text-gray-400 '
                        }`}
                    >
                        Payments
                    </button>
                </nav>
            </div>

            {/* Overview Content */}
            {activeTab === 'overview' && (  <OverviewTab/>    )}

            {/* Salespersons Content */}
            {activeTab === 'salespersons' && <SalespersonsTab setIsAddSalespersonOpen={setIsAddSalespersonOpen} isAddSalespersonOpen={isAddSalespersonOpen} />}

            {/* Referrals Content */}
            {activeTab === 'referrals' && (  <ReferralSection/>  )}

            {activeTab === 'payments' && (  <PaymentSection/>  )}

            {/* Notification Toast */}
            {notification && (
                <div className="fixed bottom-4 right-4 z-50 w-80 rounded-lg bg-white p-4 shadow-lg">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="font-medium text-gray-900">{notification.message}</h3>
                            <p className="mt-1 text-sm text-gray-500">{notification.subMessage}</p>
                        </div>
                        <button onClick={() => setNotification(null)} className="text-gray-400 hover:text-gray-500">
                            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path
                                    fillRule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clipRule="evenodd"
                                />
                            </svg>
                        </button>
                    </div>
                </div>
            )}


                  {activeTab !== 'salespersons' &&<AddSalespersonPopup
                            isOpen={isAddSalespersonOpen}
                            onClose={() => setIsAddSalespersonOpen(false)}
                       
                            mode={"add"}
                            initialData={  undefined}
                            setNotification={setNotification}
                        />}
        </div>
    );
}
