'use client';
import { Dialog, Transition } from '@headlessui/react';
import { useState, Fragment, use } from 'react';
import * as React from 'react';
import Heading from '../Heading';
import {   ReviewStarIcon } from '@/components/icon/Icon';
import ReusebleButton from '../Button';
import StatusBadge from '../StatusBandage';
import InputField from '../InputField';
import { ButtonBorder } from '../ButtonBorder';
import TextArea from '../TextArea';

const ReportReviewModel = ({ classes ,setShowReportModel}: any) => {
    const [modal2, setModal2] = useState(true);

    return (
        <>
            <Transition appear show={modal2} as={Fragment}>
                <Dialog as="div" open={modal2} onClose={() => {setModal2(false); setShowReportModel(false)}}>
                    <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black/70">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-screen max-w-5xl rounded-[15px] border border-[#e4e4e4] bg-white p-5 text-black shadow-lg">
                                <ModelData setModal2={setModal2} setShowReportModel={setShowReportModel}/>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </Dialog>
            </Transition>
        </>
    );
};
export default ReportReviewModel;
const ModelData = ({setModal2 ,setShowReportModel}:any) => {
    return (
        <div className="inline-flex   w-full flex-col items-start  justify-center   font-inter ">
            <div className="flex  flex-col items-end justify-start  self-stretch">
                <div className=" pb-5">
                    <div className="inline-flex items-center justify-start gap-5 self-stretch pr-2.5">
                        <div className="inline-flex shrink grow basis-0 flex-col items-start justify-center gap-2.5">
                            <Heading title2="Report This Review" classes="!py-2" />
                        </div>
                    </div>
                    <div>
                        <div className="inline-flex h-5 items-center justify-start gap-1">
                            <p className="font-inter text-base font-semibold leading-tight text-[#636363]">Reviewed On:</p>
                            <p className="text-center font-inter text-base font-normal leading-normal text-[#636363]">1 Dec 2024 | 02:45 PM</p>
                        </div>
                        <div className="inline-flex h-5 items-center justify-start gap-1 ps-5">
                            <p className="font-inter text-base font-semibold leading-tight text-[#636363]">Status:</p>
                            <p className="text-center font-inter text-base font-normal leading-normal text-[#636363]">
                                {' '}
                                <StatusBadge status={'Published'} />
                            </p>
                        </div>
                    </div>
                    <div className="flex   max-h-[70vh] flex-col items-start justify-start gap-2 self-stretch overflow-y-auto  ">
                        <p className="flex pt-5 font-inter text-lg font-semibold leading-tight text-[#2d2d2e]">
                            <span className="pe-5 ">Star Rating</span>
                            <span className="flex items-center  text-center font-inter text-base font-normal leading-normal text-[#636363]">
                                <ReviewStarIcon />
                                <ReviewStarIcon />
                                <ReviewStarIcon />
                                <ReviewStarIcon />
                                <ReviewStarIcon />
                                <span className="ps-2 ">4.5/5</span>
                            </span>
                        </p>
                        <p className="font-inter text-lg font-normal leading-relaxed text-[#636363]">
                            Mohammad was extremely helpful in finding my new home and made the whole process very smooth.
                            <br /> <br />
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin velit augue, hendrerit et tempor ac, rutrum in diam. Mauris semper augue nec gravida aliquam. 🙌 <br />{' '}
                            <br />
                            Thank you for your great service! 👏😍😇
                        </p>

                        <div className="mx-4 mt-3 h-[0px] self-stretch border border-[#959595]"></div>

                        <Heading title1="Write a " title2="Reason" classes="!py-2" />
                        <div className="w-full pe-2">
                            <TextArea id="address-line-2"  value="" placeholder="Write a short and specific reason to report this review.*" />
                        </div>
                    </div>
                </div>
                <div className=" flex items-center justify-end gap-5   pb-5">
                    <div className='flex justify-end gap-5'>

                    <ButtonBorder value="Cancel"  className="!w-28 h-14" onClick={()=>{setModal2(false); setShowReportModel(false)}}/>

                    <ReusebleButton text="Submit for Review"  onClick={()=>{setModal2(false); setShowReportModel(false)}}/>
                    </div>
                </div>
            </div>
        </div>
    );
};


