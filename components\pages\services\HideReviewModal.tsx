'use client';

import type React from 'react';
import { useState } from 'react';

interface HideServiceModalProps {
    service: any;
    onClose: () => void;
    onConfirm: (reason: string) => void;
}

export default function HideServiceModal({ service, onClose, onConfirm }: HideServiceModalProps) {
    const [reason, setReason] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    if (!service) return null;

    const handleSubmit = async () => {
        if (!reason.trim()) {
            return;
        }

        setIsSubmitting(true);
        try {
            await onConfirm(reason.trim());
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="w-full max-w-md mx-auto bg-white rounded-lg ">
            {/* Header */}
            <div className="flex justify-between items-start mb-4">
                <h2 className="text-lg font-semibold text-[#2d2d2e] font-inter">Hide Service</h2>
                <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600 text-xl w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100"
                >
                    ×
                </button>
            </div>

            {/* Hide Icon */}
            <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L6.5 6.5m3.378 3.378a3 3 0 013.243-2.878m0 0L16.5 4.5m-3.379 2.878L9.878 9.878m4.242 4.242L16.5 16.5m-2.378-3.378a3 3 0 01-4.243-4.243m0 0L6.5 6.5" />
                    </svg>
                </div>
            </div>

            {/* Content */}
            <div className="mb-6">
                <p className="text-sm text-[#636363] font-inter leading-relaxed mb-4">
                    Hide service titled <span className="font-medium text-[#2d2d2e]">
                        {service.title}
                    </span>
                </p>

                <div className="mb-4">
                    <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
                        Reason for hiding *
                    </label>
                    <textarea
                        id="reason"
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="Please provide a reason for hiding this service..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        rows={4}
                        required
                    />
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
                <button
                    onClick={onClose}
                    disabled={isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-[#636363] bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors font-inter disabled:opacity-50"
                >
                    Cancel
                </button>
                <button
                    onClick={handleSubmit}
                    disabled={!reason.trim() || isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-white bg-[#2d2d2e] border border-[#2d2d2e] rounded hover:bg-black transition-colors font-inter disabled:opacity-50"
                >
                    {isSubmitting ? 'Hiding...' : 'Hide Service'}
                </button>
            </div>
        </div>
    );
}
