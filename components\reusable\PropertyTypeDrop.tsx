"use client"

import { useState, useEffect, useRef } from "react"

interface Option {
  label: string
  value: string
}

interface SearchDropDownProps {
  classes?: string
  placeholder?: string
  onSelect?: (value: string) => void
  defaultValue?: string
}

type PropertyType = "Residential" | "Commercial"
type ResidentialSubtype =
  | "Apartment"
  | "Villa"
  | "Townhouse"
  | "Penthouse"
  | "Villa Compound"
  | "Hotel Apartment"
  | "Residential Plot"
  | "Residential Floor"
  | "Residential Building"

type CommercialSubtype =
  | "Office"
  | "Shop"
  | "Warehouse"
  | "Commercial Plot"
  | "Commercial Floor"
  | "Commercial Building"

type SelectedProperty = {
  type: PropertyType
  subtype: string | null
}

const PropertyTypeDrop = ({
  classes = "",
  placeholder = "Select Property Type*",
  onSelect,
  defaultValue,
}: SearchDropDownProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedProperty, setSelectedProperty] = useState<SelectedProperty | null>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const toggleDropdown = () => setIsOpen(!isOpen)

  const handlePropertySelect = (type: PropertyType, subtype: string) => {
    const selection = { type, subtype }
    setSelectedProperty(selection)
    const formattedSelection = `${type} - ${subtype}`
    if (onSelect) onSelect(formattedSelection)
    setIsOpen(false)
  }

  const getDisplayText = () => {
    if (selectedProperty && selectedProperty.subtype) {
      return `${selectedProperty.type} - ${selectedProperty.subtype}`
    }
    return placeholder
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (defaultValue) {
      const parts = defaultValue.split(" - ")
      if (parts.length === 2) {
        const type = parts[0] as PropertyType
        const subtype = parts[1]
        setSelectedProperty({ type, subtype })
      }
    }
  }, [defaultValue])

  return (
    <div ref={dropdownRef} className={`relative inline-block w-full ${classes}`}>
      <div
        onClick={toggleDropdown}
        className={`cursor-pointer flex h-14 items-center justify-between gap-2 font-inter p-2 border border-[#E4E4E4] rounded-md bg-white ${
          isOpen ? "" : ""
        }`}
      >
        <p
          className={`ps-3 ${
            selectedProperty
              ? "text-black md:text-base text-sm font-normal"
              : "text-neutral-400 md:text-base text-sm font-normal"
          }`}
        >
          {getDisplayText()}
        </p>
        <svg
          className={`w-4 h-4 transform transition-transform ${isOpen ? "rotate-180" : "rotate-0"}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      {isOpen && (
        <div className="absolute mt-2 border border-gray-200 top-[50px] z-10 w-full bg-white rounded shadow-lg">
          <PropertyTabs
            initialType={selectedProperty?.type || "Residential"}
            initialSubtype={selectedProperty?.subtype || null}
            onSelect={handlePropertySelect}
          />
        </div>
      )}
    </div>
  )
}

interface PropertyTabsProps {
  initialType: PropertyType
  initialSubtype: string | null
  onSelect: (type: PropertyType, subtype: string) => void
}

function PropertyTabs({ initialType, initialSubtype, onSelect }: PropertyTabsProps) {
  const [activeTab, setActiveTab] = useState<PropertyType>(initialType)
  const [selectedSubtypes, setSelectedSubtypes] = useState<{
    Residential: string | null
    Commercial: string | null
  }>({
    Residential: initialType === "Residential" ? initialSubtype : null,
    Commercial: initialType === "Commercial" ? initialSubtype : null,
  })

  const residentialSubtypes: ResidentialSubtype[] = [
    "Apartment",
    "Villa",
    "Townhouse",
    "Penthouse",
    "Villa Compound",
    "Hotel Apartment",
    "Residential Plot",
    "Residential Floor",
    "Residential Building",
  ]

  const commercialSubtypes: CommercialSubtype[] = [
    "Office",
    "Shop",
    "Warehouse",
    "Commercial Plot",
    "Commercial Floor",
    "Commercial Building",
  ]

  const currentSubtypes = activeTab === "Residential" ? residentialSubtypes : commercialSubtypes

  const handleSubtypeSelect = (subtype: string) => {
    setSelectedSubtypes({ ...selectedSubtypes, [activeTab]: subtype })
    onSelect(activeTab, subtype)
  }

  return (
    <div className="w-full p-4 rounded-lg border border-[#e4e4e4] bg-white shadow-sm">
      {/* Tabs */}
      <div className="flex rounded-lg overflow-hidden mb-4 border border-[#e4e4e4]">
        {(["Residential", "Commercial"] as PropertyType[]).map((tab) => (
          <button
            key={tab}
            className={`flex-1 py-2 text-sm font-medium text-center transition-all border ${
              activeTab === tab
                ? "bg-[#711d1d] text-white border-[#711d1d]"
                : "bg-white text-[#333] border-[#ccc] hover:bg-[#f9f9f9]"
            }`}
            onClick={() => {
              setActiveTab(tab)
              if (selectedSubtypes[tab]) {
                onSelect(tab, selectedSubtypes[tab]!)
              }
            }}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Grid Buttons */}
      <h2 className="text-base font-medium text-gray-800 mb-3">Select Type</h2>
      <div className="grid grid-cols-2">
        {currentSubtypes.map((subtype) => (
          <button
            key={subtype}
            onClick={() => handleSubtypeSelect(subtype)}
            className={`py-3 px-4 text-sm text-center border border-[#e4e4e4] transition-all ${
              selectedSubtypes[activeTab] === subtype
                ? "bg-[#711d1d] text-white font-medium"
                : "bg-white text-[#333] hover:text-white hover:bg-[#b33636]"
            }`}
          >
            {subtype}
          </button>
        ))}
      </div>
    </div>
  )
}

export default PropertyTypeDrop
