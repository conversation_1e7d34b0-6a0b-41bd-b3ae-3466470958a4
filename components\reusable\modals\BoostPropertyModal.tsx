"use client";
import IconCopy from "@/components/icon/icon-copy";
import Input<PERSON>ield from "../InputField";
import { CalendarIcon, CrossIcon } from "@/components/icon/Icon";
import ProgressIcon from "@/components/icon/icon-progress";
import IconPaper from "@/components/icon/icon-paper";
import { Button } from "./Button copy";
import { useState } from "react";
import { AgentDropdown } from "../AgentDropdown";
import LabelInput<PERSON>ield from "../LabelInputField";
import DateInput from "../DateInput";

interface Property {
    label: string;
    value: string;
}

export default function BoostPropertyModal({ onClose }: { onClose: () => void }) {
    const textToCopy = "FAA218239713";
    const handleCopy = () => {
        navigator.clipboard.writeText(textToCopy);
    };

    const properties: Property[] = [
        { label: "Property 1", value: "property-1" },
        { label: "Property 2", value: "property-2" },
        { label: "Property 3", value: "property-3" },
        { label: "Property 4", value: "property-4" },
        { label: "Property 5", value: "property-5" },
        { label: "Property 6", value: "property-6" },
        { label: "Property 7", value: "property-7" },
        { label: "Property 8", value: "property-8" },
    ];

    const [selectedProperties, setSelectedProperties] = useState<Property[]>([]);

    const handleSelect = (selected: Property[]) => {
        setSelectedProperties(selected);
    };

    const handleRemove = (value: string) => {
        setSelectedProperties((prev) => prev.filter((property) => property.value !== value));
    };

    return (
        <div className="max-w-md font-inter">
            <div className="flex items-center justify-between border-b pb-3">
                <div className="md:w-10/12 text-black">
                    <h2 className="text-xl font-semibold">
                        Boost Your <span className="text-redMain">Property</span>
                    </h2>
                    <p className="text-gray-600 mt-2 text-xs">
                        You have selected a <strong>Property Boost Package</strong> to enhance the reach of your property and increase visibility among 1,566 other properties.
                    </p>
                </div>
                <div className="md:w-2/12 px-4">
                    <ProgressIcon />
                </div>
            </div>

            <div className="mt-4">
                <p className="text-xs pb-1">Campaign ID:</p>
                <div className="flex items-center p-2 ">
                    <span className="text-gray-700">{textToCopy}</span>
                    <button onClick={handleCopy} className="ml-2"><IconCopy className="w-5 h-5" /></button>
                </div>
            </div>

            <div className="flex md:flex-row flex-col md:gap-0 gap-2 items-center">
                <div className="mt-2 md:w-1/3 w-full md:pr-1">
                    <DateInput placeholder="Ad Start Date*" icon={<CalendarIcon/>}/>
                </div>
                <div className="mt-2 md:w-1/3 w-full md:pl-1">
                    <DateInput placeholder="Ad End Date*" icon={<CalendarIcon/>}/>
                </div>
                <div className="mt-2 md:w-1/3 md:px-4 w-full">
                    <label className="flex items-center font-normal">
                        <input type="checkbox" className="mr-2" /> Keep Running
                    </label>
                </div>
            </div>

            <div className="py-2 pt-4">
                <LabelInputField
                    id="budget-input" label="Enter Your Budget*"
                />
            </div>

            <div className="mt-2 md:w-1/2">
                <AgentDropdown label="Select Properties*" data={properties} onHandleClick={handleSelect} />
            </div>

            <div className={`${selectedProperties.length > 0 ? "mt-4" : ""}`}>
                <div className="flex flex-wrap gap-2">
                    {selectedProperties.map((property) => (
                        <span key={property.value} className="bg-lightGray border-lineColor border text-lineColor border-opacity-30 text-xs rounded px-2 py-1 flex gap-1 items-center ">
                            {property.label}
                            <div onClick={() => handleRemove(property.value)}>
                                <CrossIcon />
                            </div>
                        </span>
                    ))}
                </div>
            </div>

            <div className="mt-4">
                <LabelInputField
                    label="Add Location(s)*"
                    id="location-input"
                />
            </div>

            <div className="mt-4">
                <LabelInputField
                    label="Keywords"
                    id="keywords-input"
                />
            </div>

            <div className="mt-4 bg-lightGray p-3 rounded-md flex gap-2">
                <IconPaper />
                <p>Based on your selection, you will be charged <span className="text-red-500 font-bold">AED 1500</span> for 15 Days.</p>
            </div>

            <div className="mt-4 flex items-start">
                <input type="checkbox" className="mr-2 mt-1" />
                <p className="text-sm text-gray-700 pb-2">
                    By checking this box, you agree to the ad payment <a href="#" className="text-blue-500 underline">terms and conditions</a>.
                </p>
            </div>

            <div className="mt-6 flex justify-end gap-3">
                <button onClick={onClose} className="text-blue-500">Cancel</button>
                <div className="md:max-w-32">
                    <Button value="Continue to Pay" className="!bg-blueMain !text-white" />
                </div>

            </div>
        </div>
    );
}