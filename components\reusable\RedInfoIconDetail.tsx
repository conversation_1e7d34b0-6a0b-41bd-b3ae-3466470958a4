import React from 'react'
import { RedInfoFillIcon } from '../icon/Icon'

const RedInfoIconDetail = ({
  text
}: any) => {
  return (
    <>
      <div className="mb-5 w-full rounded-lg border font-inter">
        <div className="flex flex-col gap-4 bg-neutral-100 md:flex-row md:gap-0">
          <div className="flex flex-col items-start gap-2 rounded-[14px] px-[30px] py-6 md:flex-row md:items-center md:gap-5">
            <RedInfoFillIcon />
            <div className="w-full text-sm font-normal leading-normal text-[#2d2d2e]">
              {text}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default RedInfoIconDetail
