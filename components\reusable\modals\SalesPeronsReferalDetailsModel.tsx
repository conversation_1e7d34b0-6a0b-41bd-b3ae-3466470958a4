"use client";

import API_ENDPOINTS from "@/app/lib/apiRoutes";
import {
  X,
  ReceiptText,
  DollarSign,
  Users,
  TrendingUp,
  Clock,
  Download,
  Mail,
} from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import SearchDropDown from "../SearchDropDown";
import { showMessage } from "@/app/lib/Alert";
import Loading from "@/components/layouts/loading";

type Referral = {
  id: number;
  code?: string;
  referred_date?: string | null;
  commissionAmount?: string | number | null;
  commission_status_name?: string | null;
  salesperson_commission_rate?: string;
  user_firstname?: string | null;
  user_middlename?: string | null;
  user_lastname?: string | null;
  user_email?: string;
  subscription_plan_name?: string | null;
  subscription_price?: string | null;
  salesperson_name?: string | null;
  salesperson_email?: string | null;
};

interface SalespersonDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  id: string | number;
}

export default function SalespersonReferralDetailsModal({
  isOpen,
  onClose,
  id,
}: SalespersonDetailsModalProps) {
  // --- Filters ---
  const [plan, setPlan] = useState<string>("All Plans");
 
  const [monthFilter, setMonthFilter] = useState<string>("All Months");

  // pagination
  const itemsPerPageOptions = [5, 10, 25, 50];
  const [pageSize, setPageSize] = useState<number>(10);
  const [page, setPage] = useState<number>(1);

  // data
  const [data, setData] = useState<Referral[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [hasServerPagination, setHasServerPagination] = useState<boolean>(false);

  const [monthOptions, setMonthOptions] = useState<string[]>([]);
  const [plansOptions, setPlansOptions] = useState<string[]>([]);
 
  const [salesPerson, setSalesPerson] = useState<Referral | null>(null);
  const [commissionDetails, setCommissionDetails] = useState({
    totalCommission: 0,
    referralsCount: 0,
    totalCommissionPending: 0,
    earnedCommission: 0,
  });

  // helpers
  const currency = (num: number) => {
    if (!isFinite(num)) return "-";
    return num.toLocaleString(undefined, { style: "currency", currency: "AED", maximumFractionDigits: 2 });
  };

  const safeDate = (d?: string | null) => {
    if (!d) return "-";
    const dt = new Date(d);
    if (isNaN(dt.getTime())) return String(d);
    return dt.toLocaleDateString();
  };

  // Derived values
  const salespersonName = salesPerson?.salesperson_name || data[0]?.salesperson_name || "Agent";
  const salespersonEmail = salesPerson?.salesperson_email || data[0]?.salesperson_email || "";

  // --- fetch function ---
  const fetchSalespersons = async () => {
    if (!id) return;
    setLoading(true);

    try {
      const params = new URLSearchParams();
      params.append("page", page.toString());
      params.append("pageSize", pageSize.toString());

      // Many backends accept either a specific month param or a filter column/value pair.
      if (monthFilter !== "All Months") {
        params.append("filterColumn", "created_at");
        params.append("monthAndYear", monthFilter);
       
   
      }

      if (plan !== "All Plans") {
        params.append("plan", plan);
      } 

      const res = await fetch(`${API_ENDPOINTS.SALESPERSONS_COMMISSIONS}/${id}?${params.toString()}`, {
        credentials: "include",
      });
      const result = await res.json();

      // Normalize result
      const records: Referral[] = Array.isArray(result.data)
        ? result.data
        : Array.isArray(result.data?.records)
        ? result.data.records
        : [];

      const pagination = result.pagination ?? result.data?.pagination ?? null;

      setData(records);
      setTotalCount(pagination?.total ?? records.length ?? 0);
      setHasServerPagination(Boolean(pagination));

      setMonthOptions(
        result.availableMonths
          ? ["All Months", ...result.availableMonths ]
          : ["All Months"]
      );
      
      setPlansOptions(
        result.availablePlans
          ?["All Plans", ...result.availablePlans ]  
          : ["All Plans"]
      );
      
 

      setSalesPerson(result.salesperson ?? records[0] ?? null);

      // If backend provided pagination info, align our page/pageSize with it (don't force reset)
      if (pagination) {
        if (typeof pagination.page === "number") setPage(pagination.page);
        if (typeof pagination.pageSize === "number") setPageSize(pagination.pageSize);
      }

      setCommissionDetails({
        totalCommission: Number( result?.totals?.total_subscription_value?? 0),
        totalCommissionPending: Number(result?.totals?.total_pending_commission ?? 0),
        earnedCommission: Number(result?.totals?.total_paid_commission ?? 0)  ,
        referralsCount: Number(result?.totals?.total_referrals   ?? 0),
      });
    } catch (err) {
      console.error("Failed to fetch salespersons:", err);
      setData([]);
      setTotalCount(0);
      setHasServerPagination(false);
    } finally {
      setLoading(false);
      
    }
  };

  // Fetch whenever relevant state changes
  useEffect(() => {
    fetchSalespersons();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, page, pageSize, plan, monthFilter]);

 
  useEffect(() => {
    setPage(1);
  }, [plan, monthFilter, pageSize]);

 
  const pageRows = useMemo(() => {
    if (hasServerPagination) return data;
    const start = (page - 1) * pageSize;
    return data.slice(start, start + pageSize);
  }, [data, page, pageSize, hasServerPagination]);

  const totalPages = Math.max(1, Math.ceil((totalCount || pageRows.length) / pageSize));
  const currentPage = page;

  // Smart pager (first, last, neighbors, ellipsis)
  const pager = useMemo(() => {
    const pages: (number | "...")[] = [];
    const delta = 2; // how many pages to show around current
    const left = Math.max(1, currentPage - delta);
    const right = Math.min(totalPages, currentPage + delta);

    if (left > 1) {
      pages.push(1);
      if (left > 2) pages.push("...");
    }

    for (let p = left; p <= right; p++) pages.push(p);

    if (right < totalPages) {
      if (right < totalPages - 1) pages.push("...");
      pages.push(totalPages);
    }

    return pages;
  }, [currentPage, totalPages]);

  // --- CSV download (exports currently visible rows) ---
  const handleDownloadCSV = () => {
    const rows = pageRows.map((r) => ({
      referralId: r.id,
      client: [r.user_firstname, r.user_middlename, r.user_lastname].filter(Boolean).join(" ") || r.user_email || "-",
      plan: r.subscription_plan_name || "-",
      subscriptionValue: r.subscription_price || "",
      rate: r.salesperson_commission_rate || "",
      commission: r.commissionAmount ?? "",
      date: safeDate(r.referred_date ?? ""),
      status: r.commission_status_name ?? "",
    }));

    if (rows.length === 0) return;

    const header = Object.keys(rows[0]).join(",");
    const csv = [
      header,
      ...rows.map((row) =>
        Object.values(row)
          .map((v) => {
            if (v === null || v === undefined) return "";
            const s = String(v).replace(/"/g, '""');
            if (s.includes(",") || s.includes("\n")) return `"${s}"`;
            return s;
          })
          .join(",")
      ),
    ].join("\n");

    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `commission-statement-${String(salespersonName).replace(/\s+/g, "-").toLowerCase()}.csv`;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
  };

  const handleSendToAgent =async () => {
   
        try {
            const params = new URLSearchParams(); 
            if (monthFilter !== "All Months") {
              params.append("filterColumn", "created_at");
              params.append("monthAndYear", monthFilter); 
            } 
            if (plan !== "All Plans") {
              params.append("plan", plan);
            }  
            const res = await fetch(`${API_ENDPOINTS.SALESPERSONS_COMMISSIONS}/reports/${id}?${params.toString()}`, {
              credentials: "include",
            });
            const result = await res.json(); 
            if(result.success){  
              showMessage('Commission Details sent successfully', 'success');
            }else{
              showMessage(result.message, 'error');
            } 
          } catch (err) {
            console.error("Failed to fetch salespersons:", err); 
          }  
      
  };

  if (!isOpen) return null;

  return (
    <>
    {
      loading && <Loading />
    }
    <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative w-full max-w-[1100px] overflow-hidden rounded-2xl   bg-white shadow-lg max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 px-6 pb-4 pt-6">
          <h2 className="text-lg font-semibold text-[#2d2d2e]">Salesperson Details</h2>
          <button
            onClick={onClose}
            className="rounded-md p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
            aria-label="Close"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="relative mx-auto  ">
          {/* Modal header & controls */}
          <div className="   w-full">
            <div className="flex flex-col gap-4 p-6 sm:flex-row sm:items-center sm:justify-between border-b">
              <div className="flex items-center gap-3">
                <div className="rounded-xl border bg-neutral-100 p-2">
                  <ReceiptText className="h-5 w-5 text-neutral-500" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold leading-tight  ">
                    Commission Statement
                    <span className="text-neutral-500">{" - "}{salesPerson?.salesperson_name || salespersonName}</span>
                  </h1>

                  
                </div>
              </div>
              

              <div className="flex items-center gap-2">
                <button
                  onClick={handleDownloadCSV}
                  className="inline-flex items-center gap-2 rounded-md border px-3 py-2 text-sm font-medium hover:bg-neutral-50"
                >
                  <Download className="h-4 w-4" />
                  <span>Download CSV</span>
                </button>
                <button
                  onClick={handleSendToAgent}
                  className="inline-flex items-center gap-2 rounded-md bg-black px-3 py-2 text-sm font-medium text-white hover:bg-black/90"
                >
                  <Mail className="h-4 w-4" />
                  <span>Send to Agent</span>
                </button>
              </div>
            </div>
            <div className="flex w-full flex-col  flex-wrap items-center gap-5 md:flex-row px-6 pt-4">
                    <div className="w-auto min-w-[230px]  md:min-w-[287px] flex-1 md:max-w-[287px]">
                      <SearchDropDown
                        classes="!h-14 w-full"
                        dropdownOptions={plansOptions.map((m) => ({ label: m,  }))}
                        initail={plan}
                        setSelectedStatus={(v: string) => setPlan(v)}
                      />
                    </div>

                    <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                      <SearchDropDown
                        classes="!h-14 w-full"
                        dropdownOptions={monthOptions.map((m) => ({ label: m,  }))}
                        initail={monthFilter}
                        setSelectedStatus={(v: string) => setMonthFilter(v)}
                      />
                    </div> 
                  </div>

            {/* KPIs */}
            <div className="grid gap-4 p-6 sm:grid-cols-2 lg:grid-cols-4">
              <div className="rounded-xl border border-dashed p-4">
                <div className="text-sm text-neutral-600 flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  <span>Commission Earned</span>
                </div>
                <div className="mt-2 text-2xl font-bold text-emerald-600">{currency(commissionDetails.earnedCommission)}</div>
              </div>

              <div className="rounded-xl border border-dashed p-4">
                <div className="text-sm text-neutral-600 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>Commission Pending</span>
                </div>
                <div className={`mt-2 text-2xl font-bold   text-amber-600   `}>{currency(commissionDetails.totalCommissionPending)}</div>
              </div>

              <div className="rounded-xl border border-dashed p-4">
                <div className="text-sm text-neutral-600 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>Referrals</span>
                </div>
                <div className="mt-2 text-2xl font-bold">{commissionDetails.referralsCount ?? totalCount}</div>
              </div>

              <div className="rounded-xl border border-dashed p-4">
                <div className="text-sm text-neutral-600 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  <span>Total Value</span>
                </div>
                <div className="mt-2 text-2xl font-bold">{currency(commissionDetails.totalCommission)}</div>
              </div>
            </div>

            {/* Commission Details table */}
            <div className="px-6 pb-6">
              <div className="rounded-xl border">
                <div className="px-4 py-3 border-b">
                  <div className="text-lg font-semibold">Commission Details</div>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="text-left text-neutral-500">
                      <tr className="border-b">
                        <th className="px-4 py-3 w-[120px]">Referral ID</th>
                        <th className="px-4 py-3">Client</th>
                        <th className="px-4 py-3">Plan</th>
                        <th className="px-4 py-3 text-right">Subscription Value</th>
                        <th className="px-4 py-3 text-right">Rate</th>
                        <th className="px-4 py-3 text-right">Commission</th>
                        <th className="px-4 py-3 text-right">Date</th>
                        <th className="px-4 py-3 text-right">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {loading ? (
                        <tr>
                          <td colSpan={8} className="px-4 py-8 text-center text-neutral-500">
                            Loading...
                          </td>
                        </tr>
                      ) : pageRows.length === 0 ? (
                        <tr>
                          <td colSpan={8} className="px-4 py-8 text-center text-neutral-500">
                            No referrals found.
                          </td>
                        </tr>
                      ) : (
                        pageRows.map((r) => {
                          const clientName =
                            [r.user_firstname, r.user_middlename, r.user_lastname].filter(Boolean).join(" ").trim() ||
                            r.user_email ||
                            "-";
                          const value = r.subscription_price ? Number(r.subscription_price) : 0;
                          const commission = r.commissionAmount ? Number(r.commissionAmount) : 0;
                          const status = r.commission_status_name ?? "-";
                          return (
                            <tr key={r.id} className="border-b last:border-0">
                              <td className="px-4 py-3">
                                <span className="inline-flex items-center rounded-md border bg-neutral-50 px-2 py-1 text-xs font-medium text-neutral-700">
                                  {r.code}
                                </span>
                              </td>
                              <td className="px-4 py-3 font-medium">{clientName}</td>
                              <td className="px-4 py-3 text-neutral-600">{r.subscription_plan_name || "-"}</td>
                              <td className="px-4 py-3 text-right font-medium">{currency(value)}</td>
                              <td className="px-4 py-3 text-right">
                                <span className="inline-flex items-center rounded-full border px-2 py-0.5 text-xs">{r.salesperson_commission_rate}</span>
                              </td>
                              <td className={`px-4 py-3 text-right font-medium text-emerald-600 `}>{commission ? currency(commission) : "-"}</td>
                              <td className="px-4 py-3 text-right text-neutral-600">{safeDate(r.referred_date)}</td>
                              <td className="px-4 py-3 text-right">
                                {status === "-" ? (
                                  <span className="text-neutral-400">-</span>
                                ) : (
                                  <span
                                    className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs capitalize ${
                                      status.toLowerCase() === "earned" ? "bg-emerald-100 text-emerald-700" : "bg-amber-100 text-amber-700"
                                    }`}
                                  >
                                    {status}
                                  </span>
                                )}
                              </td>
                            </tr>
                          );
                        })
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Table footer: pagination controls */}
                <div className="flex flex-col gap-3 items-start justify-between border-t px-4 py-3 sm:flex-row sm:items-center">
                  <div className="flex items-center gap-3">
                    <label htmlFor="pageSize" className="text-sm text-neutral-600">
                      Rows per page
                    </label>
                    <select
                      id="pageSize"
                      className="h-8 rounded-md border bg-white px-2 text-sm"
                      value={pageSize}
                      onChange={(e) => setPageSize(Number(e.target.value))}
                    >
                      {itemsPerPageOptions.map((n) => (
                        <option key={n} value={n}>
                          {n}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => setPage((p) => Math.max(1, p - 1))}
                        disabled={currentPage === 1}
                        className="h-8 w-8 rounded-md border text-sm disabled:opacity-50"
                        aria-label="Previous page"
                      >
                        {"<"}
                      </button>

                      {/* Numbered pages with ellipsis support */}
                      {pager.map((p, idx) =>
                        p === "..." ? (
                          <span key={`dots-${idx}`} className="px-2 text-sm text-neutral-500">...</span>
                        ) : (
                          <button
                            key={p}
                            onClick={() => setPage(Number(p))}
                            className={`h-8 min-w-8 rounded-md border px-2 text-sm ${p === currentPage ? "bg-black text-white" : "hover:bg-neutral-50"}`}
                            aria-current={p === currentPage ? "page" : undefined}
                          >
                            {p}
                          </button>
                        )
                      )}

                      <button
                        onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                        disabled={currentPage === totalPages}
                        className="h-8 w-8 rounded-md border text-sm disabled:opacity-50"
                        aria-label="Next page"
                      >
                        {">"}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Info Footer */}
              {/* <div className="mt-6 grid gap-6 rounded-xl border p-4 md:grid-cols-2">
                <div>
                  <div className="font-medium">Payment Information</div>
                  <p className="mt-2 text-sm text-neutral-600">
                    Commissions are paid monthly on the 30th of the following month. Earned commissions will be included in
                    the next payment cycle.
                  </p>
                </div>
                <div>
                  <div className="font-medium">Contact Information</div>
                  <p className="mt-2 text-sm text-neutral-600">
                    For questions about your commission statement, <NAME_EMAIL> or call (555) 123-4567.
                  </p>
                </div>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
