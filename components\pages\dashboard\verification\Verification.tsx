'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';

import { ButtonBorder } from '@/components/reusable/ButtonBorder';
import ProfileInfo from '../components/ProfileInfo';
import SubscriptionInfo from '../components/SubscribtionInfo';
import CardInfo from '../components/CardInfo';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import DocSubmitModal from '@/components/reusable/modals/DocSubmitModal';
import { useState } from 'react';
import DocumentDetail from '../components/DocumentDetail';

export default function VerificationDetails() {
    const router = useRouter();
    const [isModalOpen, setIsModalOpen] = useState(false);
    return (
        <DefaultPageLayout>
            <div className="relative grid grid-cols-1 md:grid-cols-12 gap-3 pt-10 font-inter px-4 md:pb-32 pb-72">
                {/* Left Section (Profile Info & Documents) */}
                <div className="md:col-span-9">
                    {/* Content Section */}
                    <div className="md:pb-24">
                        <DocumentDetail />
                    </div>
                </div>

                {/* Right Sidebar Section (Subscription & Card Info) */}
                <div className="md:col-span-3 flex flex-col gap-5 md:pt-12">
                    <SubscriptionInfo />
                    <CardInfo />
                </div>
            </div>
            <DocSubmitModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
        </DefaultPageLayout>
    );
}
