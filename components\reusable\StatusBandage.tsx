export const statusStyles: { [key: string]: { border: string; bg: string; text: string; label: string } } = {
    Published: { border: '#2ab140', bg: '#f2fff4', text: '#2ab140', label: 'Published' },
    Scheduled: { border: '#00c7be', bg: '#e6fffd', text: '#00c7be', label: 'Scheduled' },
    Draft: { border: '#959595', bg: '#f5f5f5', text: '#959595', label: 'Draft' },
    'In Approval': { border: '#5856d6', bg: '#f5f5ff', text: '#5856d6', label: 'In Approval' },
    'In Progress': { border: '#ec8a00', bg: '#fef5e8', text: '#ec8a00', label: 'In Progress' },
    Available: { border: '#2ab140', bg: '#f2fff4', text: '#2ab140', label: 'Available' },
    'Not-Available': { border: '#ff3b30', bg: '#fff0f0', text: '#ff3b30', label: 'Not Available' },
    'Off-Plan': { border: '#a2845e', bg: '#a2845e', text: '#ffffff', label: 'Off-Plan' },
    Ready: { border: '#2ab140', bg: '#2ab140', text: '#ffffff', label: 'Ready' },
    Rent: { border: '#A2845E', bg: '#A2845E', text: '#ffffff', label: 'Rent' },
    Sale: { border: '#AF52DE', bg: '#AF52DE', text: '#ffffff', label: 'Sale' },
    Rented: { border: '#933', bg: '#FFF5F5', text: '#933', label: 'Rented' },
    Sold: { border: '#933', bg: '#FFF5F5', text: '#933', label: 'Rented' },
    Unpublished: { border: '#933', bg: '#FFF5F5', text: '#933', label: 'Unpublished' },
    Removed: { border: '#993333', bg: '#fff4f4', text: '#993333', label: 'Removed' },
    Review: { border: '##ec8a00', bg: '#fef5e8', text: '#ec8a00', label: 'Review' },
    Online: { border: '#2AB141', bg: '#F3FFF5', text: '#2AB141', label: 'Online' },
    Paid: { border: '#2ab140', bg: '#f2fff4', text: '#2ab140', label: 'Paid' },
    Unpaid: { border: '#933', bg: '#FFF5F5', text: '#933', label: 'Unpaid' },
    Pro: { border: '#00C7BE', bg: '#00C7BE', text: '#ffffff', label: 'Pro' },
    Standard: { border: '#ED8A00', bg: '#ED8A00', text: '#ffffff', label: 'Standard' },
    Elite: { border: '#5856D6', bg: '#5856D6', text: '#ffffff', label: 'Elite' },
    Incomplete: { border: '#993333', bg: '#fff4f4', text: '#993333', label: 'Incomplete' },

    // ✅ New Statuses
    Activated: { border: '#2ab140', bg: '#f2fff4', text: '#2ab140', label: 'Approved' },
    Rejected: { border: '#933', bg: '#FFF5F5', text: '#933', label: 'Rejected' },
    Pending: { border: '#ec8a00', bg: '#fef5e8', text: '#ec8a00', label: 'Pending' },
    Suspended: { border: '#ff9500', bg: '#fff8e6', text: '#ff9500', label: 'Suspended' },
    Archived: { border: '#959595', bg: '#f5f5f5', text: '#959595', label: 'Archived' },

    // For Packages
    Active: { border: '#2d2d2e', bg: '#2d2d2e', text: '#ffffff', label: 'Active' },
    Inactive: { border: '#9f9fa7', bg: '#9f9fa7', text: '#ffffff', label: 'Inactive' },

    Verified: {
        border: '#2ab140',
        bg: '#E6F4EA', // Soft green background
        text: '#2ab140',
        label: 'Verified',
    },

    'Expired Soon': {
        border: '#D97706',
        bg: '#FFF8E6', // Light amber background
        text: '#D97706',
        label: 'Expired Soon',
    },
    Expired: {
        border: '#6B7280',
        bg: '#F5F5F5', // Light gray background
        text: '#6B7280',
        label: 'Expired',
    },
};

const StatusBadge = ({ status }: { status: string }) => {
    const style = statusStyles[status] || {};

    if (!style.border) return null;

    return (
        <span
            className="bg-n  inline-flex items-center justify-center gap-2.5 rounded border px-2 py-1 text-center font-inter text-sm font-medium"
            style={{ borderColor: style.border, backgroundColor: style.bg, color: style.text }}
        >
            {style.label}
        </span>
    );
};

export default StatusBadge;
