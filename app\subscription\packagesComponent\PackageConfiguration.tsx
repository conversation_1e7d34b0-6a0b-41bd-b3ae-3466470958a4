import React, { useState, useEffect } from 'react';
import { COLOR_THEMES, ColorThemeOptions } from '@/components/subscription/ColorThemeEnum';
import StatusBadge from '@/components/reusable/StatusBandage';
import { Plus, Settings } from 'lucide-react';
import Modal from '@/components/reusable/modals/modal';
import InputField from '@/components/reusable/InputField';
import TextArea from '@/components/reusable/TextArea';
import { Dropdown } from '@/components/reusable/Dropdown';
import ReusebleButton from '@/components/reusable/Button';
import PackageConfiguratorModal from './PackageConfiguratorModal';
import axios from 'axios';
import { DISCOUNTS_API, PACKAGES_API, STATUS_API } from '@/app/lib/apiRoutes';
import { Package } from '@/app/types/subscription';
import { showMessage } from '@/app/lib/Alert';
import { Discount } from '@/components/pages/discout-management/DiscountManagement';

type ApiResponse<T> = { data: T; [key: string]: any };

const PackageConfiguration = () => {
    const [packages, setPackages] = useState<Package[]>([]);
    const [showAddModal, setShowAddModal] = useState(false);
    const [showConfiguratorModal, setShowConfiguratorModal] = useState(false);
    const [selectedPackage, setSelectedPackage] = useState<any | null>(null);
    const [discountOptions, setDiscountOptions] = useState<{ value: number; label: string }[]>([]);

    const [newPackage, setNewPackage] = useState({
        name: '',
        price: '',
        colorTheme: 'blue',
        usertype: 'agent',
        interval: 'month',
        active: true,
        description: '',
        discountId: null,
    });

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [statuses, setStatuses] = useState<{ id: number; name: string }[]>([]);

    const fetchPackages = async () => {
        try {
            setShowConfiguratorModal(false);
            setSelectedPackage(null);
            const res = await axios.get<ApiResponse<Package[]>>(PACKAGES_API, { withCredentials: true });
            setPackages(res.data.data ?? []);
        } catch (err) {
            setPackages([]);
        }
    };

    useEffect(() => {
        const fetchStatuses = async () => {
            try {
                const res = await axios.get(STATUS_API);
                setStatuses((res.data as any).data);
            } catch (err) {
                setStatuses([]); // fallback to empty array
            }
        };

        const fetchDiscounts = async () => {
            try {
                const res = await axios.get(DISCOUNTS_API, { withCredentials: true });

                const activeOnly = (res.data as any).data;

                const options = activeOnly.map((d: Discount) => {
                    const priceDisplay = d.type === 'fixed' ? `${d.value} AED` : `${d.value}%`;
                    return {
                        value: d.id,
                        label: `${d.name} (${priceDisplay})`,
                    };
                });

                setDiscountOptions(options);
            } catch (err) {
                console.error('Failed to load discounts', err);
                setDiscountOptions([]);
            }
        };

        fetchPackages();
        fetchStatuses();
        fetchDiscounts();
    }, []);

    const handleAddPackage = async () => {
        setLoading(true);
        setError(null);
        try {
            const price = Number(newPackage.price);

            // ✅ Validate price
            if (isNaN(price) || price <= 0) {
                showMessage('Package price must be greater than zero.', 'error');
                setLoading(false);
                return;
            }

            // ✅ If a discount is selected, validate that it won't zero or negatively affect the price
            if (newPackage.discountId) {
                const discount = discountOptions.find((d) => d.value === newPackage.discountId);
                if (discount) {
                    const isPercentage = discount.label.includes('%');
                    const discountValue = parseFloat(discount.label.match(/[\d.]+/)?.[0] || '0');

                    let finalPrice = isPercentage ? price - (price * discountValue) / 100 : price - discountValue;

                    if (finalPrice <= 0) {
                        showMessage(`Final price after applying discount is ${finalPrice.toFixed(2)} AED, which is not allowed. It must be greater than zero.`, 'error');
                        setLoading(false);
                        return;
                    }
                }
            }

            // Fetch statusId for Active/Inactive
            const statusRes = await axios.get(STATUS_API);
            const statusList = (statusRes.data as any).data;
            const statusObj = statusList.find((s: any) => s.name === (newPackage.active ? 'Activated' : 'Deactivated'));
            const statusId = statusObj ? statusObj.id : 1;

            // Prepare request body
            const reqBody = {
                name: newPackage.name,
                description: newPackage.description,
                statusId,
                createdBy: 1, // TODO: Replace with actual user ID if available
                price,
                currency: 'AED',
                userType: newPackage.usertype,
                colorTheme: newPackage.colorTheme,
                interval: newPackage.interval,
                discountId: newPackage.discountId || null,
            };
            await axios.post(PACKAGES_API, reqBody, {
                withCredentials: true,
            });
            // Fetch updated packages
            const pkgRes = await axios.get<ApiResponse<Package[]>>(PACKAGES_API, {
                withCredentials: true,
            });
            setPackages(pkgRes.data.data ?? []);
            setShowAddModal(false);
            setNewPackage({
                name: '',
                price: '',
                colorTheme: 'blue',
                usertype: 'agent',
                interval: 'month',
                active: true,
                description: '',
                discountId: null,
            });
        } catch (err: any) {
            const msg = err.response?.data?.message || 'Failed to add package';
            showMessage(msg, 'error'); // ✅ Display error message as toast/alert
            setError(msg); // Optional: still set local error state
        } finally {
            setLoading(false);
        }
    };

    const handleConfigure = (pkg: any) => {
        // For demo, add a 'features' object with default values if not present
        setSelectedPackage({
            ...pkg,
            features: pkg.features || {
                Badge: pkg.name || '',
                'Listings per Seat': 5,
                'Auto-Leads Priority': 'Lowest',
                'Visibility Level': 'Low',
                'Front/Landing Page Listing': 'Not Listed',
                'Listing Management': true,
                'CRM Management': false,
                'Live Chat': false,
                'Reward Badges': true,
                'Risk of Being Blacklisted': 'High',
            },
            description: pkg.description || '',
            price: typeof pkg.price === 'string' && pkg.price.match(/\d+/) ? parseInt(pkg.price) : pkg.price,
        });
        setShowConfiguratorModal(true);
    };

    const userTypeOptions = [
        { label: 'Agent', value: 'agent' },
        { label: 'Agency', value: 'agency' },
    ];

    const packageIntervalOptions = [
        { label: 'Monthly', value: 'month' },
        { label: 'Yearly', value: 'year' },
    ];

    return (
        <div className="space-y-6 font-inter">
            {/* Header with Add Button */}
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-[#2d2d2e]">Packages</h2>
                <button onClick={() => setShowAddModal(true)} className="flex items-center rounded-lg bg-[#993333] px-5 py-2 text-base font-medium text-white transition-all hover:bg-[#711d1d]">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Package
                </button>
            </div>
            {/* Package Listing Table */}
            <div className="rounded-lg border border-[#e4e4e4] bg-white">
                <div className="border-b border-[#e4e4e4] px-6 py-4">
                    <h3 className="text-lg font-semibold text-[#2d2d2e]">Package Listing</h3>
                </div>
                <div className="overflow-x-auto">
                    <table className="w-full border-collapse font-inter">
                        <thead className="bg-[#e4e4e4]">
                            <tr>
                                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Order</th>
                                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Package Name</th>
                                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">User Type</th>
                                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Interval</th>
                                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Price (AED)</th>
                                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Color Theme</th>
                                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Status</th>
                                <th className="h-[56px] px-4 text-right text-base font-medium text-[#2d2d2e]">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {packages &&
                                packages.length > 0 &&
                                packages.map((pkg, index) => (
                                    <tr key={pkg.id} className="border-b border-[#e4e4e4] hover:bg-neutral-100">
                                        <td className="h-[56px] px-4 align-middle">
                                            <div className="flex items-center pl-4">{index + 1}</div>
                                        </td>
                                        <td className="h-[56px] px-4 align-middle font-semibold text-[#2d2d2e]">{pkg.name}</td>
                                        <td className="h-[56px] px-4 align-middle text-[#2d2d2e]">{pkg.userType}</td>
                                        <td className="h-[56px] px-4 align-middle text-[#2d2d2e]">{pkg.interval}</td>
                                        <td className="h-[56px] px-4 align-middle text-[#2d2d2e]">{pkg.price}</td>
                                        <td className="h-[56px] px-4 align-middle">
                                            <div className="flex items-center">
                                                <div className={`mr-2 h-3 w-3 rounded-full ${COLOR_THEMES[pkg.colorTheme]}`}></div>
                                                <span className="capitalize text-[#636363]">{pkg.colorTheme}</span>
                                            </div>
                                        </td>
                                        <td className="h-[56px] px-4 align-middle">
                                            {(() => {
                                                const statusObj = statuses.find((s) => s.id === pkg.statusId);
                                                return <StatusBadge status={statusObj?.name === 'Activated' ? 'Active' : 'Inactive'} />;
                                            })()}
                                        </td>
                                        <td className="h-[56px] px-4 text-right align-middle">
                                            <button
                                                onClick={() => handleConfigure(pkg)}
                                                className="inline-flex items-center gap-2 rounded border border-[#e4e4e4] bg-white px-3 py-2 font-semibold text-[#2d2d2e] transition-all hover:bg-neutral-100"
                                            >
                                                <Settings className="mr-1 h-4 w-4" />
                                                <span className="ml-1 text-base font-medium">Configure</span>
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Add Package Modal */}
            <Modal isOpen={showAddModal} onClose={() => setShowAddModal(false)}>
                <div className="mb-6 flex items-center justify-between">
                    <h2 className="text-2xl font-bold text-[#2d2d2e]">Add New Package</h2>
                    <button onClick={() => setShowAddModal(false)} className="text-2xl text-[#993333]">
                        &times;
                    </button>
                </div>
                <div className="mb-4 grid grid-cols-2 gap-4">
                    <InputField
                        id="name"
                        label="Package Name"
                        value={newPackage.name}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewPackage({ ...newPackage, name: e.target.value })}
                        placeholder="Enter package name"
                    />
                    <Dropdown
                        label="User Type"
                        data={userTypeOptions}
                        value={userTypeOptions.find((opt) => opt.value === newPackage.usertype) || userTypeOptions[0]}
                        onHandleClick={(selected: any) => setNewPackage({ ...newPackage, usertype: selected.value })}
                        width="w-full z-[9999]"
                    />
                </div>
                <div className="mb-4 grid grid-cols-2 gap-4">
                    <Dropdown
                        label="Package Interval"
                        data={packageIntervalOptions}
                        value={packageIntervalOptions.find((opt) => opt.value === newPackage.interval) || packageIntervalOptions[0]}
                        onHandleClick={(selected: any) => setNewPackage({ ...newPackage, interval: selected.value })}
                        width="w-full z-[9999]"
                    />
                    <InputField
                        id="price"
                        label="Price (AED)"
                        type="number"
                        value={newPackage.price}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            const newPrice = e.target.value;
                            const numericPrice = Number(newPrice);
                            setNewPackage({
                                ...newPackage,
                                price: newPrice,
                                discountId: numericPrice === 0 ? null : newPackage.discountId,
                            });
                        }}
                        placeholder="0"
                    />
                </div>

                {newPackage.price && Number(newPackage.price) !== 0 && (
                    <div className="mb-4 grid grid-cols-1 gap-4">
                        <Dropdown
                            label="Select Discount (Optional)"
                            data={discountOptions}
                            value={discountOptions.find((opt) => opt.value === newPackage.discountId) || { value: null, label: 'None' }}
                            onHandleClick={(selected: any) => setNewPackage({ ...newPackage, discountId: selected.value })}
                            width="w-full z-[9999]"
                        />
                    </div>
                )}

                <TextArea
                    id="description"
                    label="Description"
                    value={newPackage.description}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setNewPackage({ ...newPackage, description: e.target.value })}
                    placeholder="Enter package description"
                    rows={3}
                    className="mb-4"
                />
                <div className="mb-6 grid grid-cols-2 items-end gap-4">
                    <div>
                        <Dropdown
                            label="Color Theme"
                            data={ColorThemeOptions.map((opt) => ({
                                value: opt.value,
                                label: (
                                    <div className="flex items-center">
                                        <div className={`mr-2 h-4 w-4 rounded ${opt.className}`}></div>
                                        {opt.label}
                                    </div>
                                ),
                            }))}
                            value={ColorThemeOptions.find((opt) => opt.value === newPackage.colorTheme)}
                            onHandleClick={(selected: any) => setNewPackage({ ...newPackage, colorTheme: selected.value })}
                            width="w-full z-[9999]"
                        />
                    </div>
                    <div className="flex items-center gap-3">
                        <label className="relative inline-flex cursor-pointer select-none items-center">
                            <input type="checkbox" checked={newPackage.active} onChange={(e) => setNewPackage({ ...newPackage, active: e.target.checked })} className="peer sr-only" />
                            <div className={`relative h-6 w-10 rounded-full transition-colors duration-200 ${newPackage.active ? 'bg-[#2d2d2e]' : 'bg-[#9f9fa7]'}`}>
                                <div
                                    className={`absolute left-0.5 top-0.5 h-5 w-5 rounded-full bg-white shadow-md transition-transform duration-200
                  ${newPackage.active ? 'translate-x-4' : ''}
                `}
                                ></div>
                            </div>
                            <span className={`ml-3 text-base font-medium`}>{newPackage.active ? 'Active' : 'Inactive'}</span>
                        </label>
                    </div>
                </div>
                <div className="mt-2 flex justify-end gap-3">
                    <ReusebleButton onClick={() => setShowAddModal(false)} text="Cancel" className="min-w-[120px] bg-[#993333] hover:bg-[#711d1d]" />
                    <ReusebleButton onClick={handleAddPackage} text="Add Package" className="min-w-[120px] bg-[#993333] hover:bg-[#711d1d]" />
                </div>
            </Modal>

            {/* Edit Package Modal Skeleton */}
            {showConfiguratorModal && selectedPackage && (
                <PackageConfiguratorModal
                    isOpen={showConfiguratorModal}
                    onClose={() => {
                        setShowConfiguratorModal(false);
                        setSelectedPackage(null);
                    }}
                    packageData={selectedPackage}
                    onSave={fetchPackages}
                    statuses={statuses}
                />
            )}
        </div>
    );
};

export default PackageConfiguration;
