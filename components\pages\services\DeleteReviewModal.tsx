'use client';

import type React from 'react';
import { useToast } from '@/components/reusable/Notify';

interface DeleteReviewModalProps {
    review: any;
    onClose: () => void;
    onConfirm: () => void;
}

export default function DeleteReviewModal({ review, onClose, onConfirm }: DeleteReviewModalProps) {
    const { showToast } = useToast();

    if (!review) return null;

    // Helper function to get display name with email fallback
    const getDisplayName = (firstName?: string, lastName?: string, email?: string, fallback?: string) => {
        if (firstName && lastName) {
            return `${firstName} ${lastName}`;
        }
        return email || fallback || 'Unknown';
    };

    const reviewerDisplayName = getDisplayName(
        review.reviewerFirstName,
        review.reviewerLastName,
        review.reviewerEmail,
        review.reviewer
    );

    const revieweeDisplayName = getDisplayName(
        review.revieweeFirstName,
        review.revieweeLastName,
        review.revieweeEmail,
        review.reviewee || review.property
    );

    return (
        <div className="w-full max-w-md mx-auto bg-white rounded-lg ">
            {/* Header */}
            <div className="flex justify-between items-start mb-4">
                <h2 className="text-lg font-semibold text-[#2d2d2e] font-inter">Delete Review</h2>
                <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600 text-xl w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100"
                >
                    ×
                </button>
            </div>

            {/* Warning Icon */}
            <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.664-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
            </div>

            {/* Content */}
            <div className="text-center mb-6">
                <p className="text-sm text-[#636363] font-inter leading-relaxed">
                    Are you sure you want to delete the review by '<span className="font-medium text-[#2d2d2e]">
                        {reviewerDisplayName}
                    </span>' for '<span className="font-medium text-[#2d2d2e]">
                        {revieweeDisplayName}
                    </span>'? This action cannot be undone.
                </p>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
                <button
                    onClick={onClose}
                    className="px-4 py-2 text-sm font-medium text-[#636363] bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors font-inter"
                >
                    Cancel
                </button>
                <button
                    onClick={onConfirm}
                    className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-red-600 rounded hover:bg-red-700 transition-colors font-inter"
                >
                    Delete
                </button>
            </div>
        </div>
    );
}
