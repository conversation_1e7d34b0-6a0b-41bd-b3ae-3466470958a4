"use client" 
import { useState, useEffect, create<PERSON>ontext, useContext, type ReactNode } from "react"
 
 
import { CrossIcon, VerifiedSmallIcon } from "../icon/Icon"
import { cn } from "./utils"
 
export type ToastType = "success" | "error" | "info" | "warning"

export interface ToastProps {
  id: string
  message: string
  type: ToastType
  duration?: number
}

 
type ToastContextType = {
  toasts: ToastProps[]
  showToast: (message: string, type?: ToastType, duration?: number) => void
  hideToast: (id: string) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export function ToastProvider({ children }: { children: ReactNode }) {
  const [toasts, setToasts] = useState<ToastProps[]>([])

  const showToast = (message: string, type: ToastType = "success", duration = 3000) => {
    const id = Math.random().toString(36).substring(2, 9)
    setToasts((prev) => [...prev, { id, message, type, duration }])
  }

  const hideToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }

  return (
    <ToastContext.Provider value={{ toasts, showToast, hideToast }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  )
}

export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

function ToastContainer() {
  const { toasts, hideToast } = useToast()

  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2">
      {toasts.map((toast) => (
        <Toast key={toast.id} {...toast} onClose={() => hideToast(toast.id)} />
      ))}
    </div>
  )
}

function Toast({ id, message, type, duration = 3000, onClose }: ToastProps & { onClose: () => void }) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose()
    }, duration)

    return () => clearTimeout(timer)
  }, [duration, onClose])

  const getToastStyles = () => {
    switch (type) {
      case "success":
        return "bg-green-50 border-green-100 text-green-800"
      case "error":
        return "bg-red-50 border-red-100 text-red-800"
      case "warning":
        return "bg-yellow-50 border-yellow-100 text-yellow-800"
      case "info":
        return "bg-blue-50 border-blue-100 text-blue-800"
      default:
        return "bg-green-50 border-green-100 text-green-800"
    }
  }

  const getIcon = () => {
    switch (type) {
      case "success":
        return <VerifiedSmallIcon   />
      case "error":
        return <CrossIcon   />
      case "warning":
        return <span className="h-5 w-5 text-yellow-500">⚠️</span>
      case "info":
        return <span className="h-5 w-5 text-blue-500">ℹ️</span>
      default:
        return <VerifiedSmallIcon   />
    }
  }

  return (
    <div
      className={cn(
        "animate-in fade-in slide-in-from-bottom-5 duration-300",
        "flex items-center justify-between gap-2 rounded-lg border p-4 shadow-md",
        "min-w-[300px] max-w-md",
        getToastStyles(),
      )}
    >
      <div className="flex items-center gap-3">
        {getIcon()}
        <p className="text-sm font-medium">{message}</p>
      </div>
      <button onClick={onClose} className="text-gray-500 hover:text-gray-700 focus:outline-none" aria-label="Close">
        <CrossIcon   />
      </button>
    </div>
  )
}

