export enum FeatureConstants {
    BADGE = 'BADGE',
    LISTING_COUNT = 'LISTING_COUNT',
    AUTO_LEADS_PRIORITY = 'AUTO_LEADS_PRIORITY',
    VISIBILITY_LEVEL = 'VISIBILITY_LEVEL',
    FRONT_PAGE_LISTING = 'FRONT_PAGE_LISTING',
    LISTING_MANAGEMENT = 'LISTING_MANAGEMENT',
    CRM_MANAGEMENT = 'CRM_MANAGEMENT',
    AGENT_MANAGEMENT = 'AGENT_MANAGEMENT',
    LIVE_CHAT = 'LIVE_CHAT',
    REWARD_BADGES = 'REWARD_BADGES',
    BLACKLIST_RISK = 'BLACKLIST_RISK',

    // 👇 Newly added based on sidebar
    PROPERTIES = 'PROPERTIES',
    PROJECTS = 'PROJECTS',
    SERVICES = 'SERVICES',
    EVENTS = 'EVENTS',
    PORTFOLIO = 'PORTFOLIO',
    AI_AGENTS = 'AI_AGENTS',
    LEADS = 'LEADS',
}

export enum AutoLeadsAssigningPriorityEnum {
    LOWEST = 'Lowest',
    LOW = 'Low',
    MEDIUM = 'Medium',
    HIGH = 'High',
    HIGHEST = 'Highest',
}

export enum VisibilityLevelEnum {
    LOWEST = 'Lowest',
    LOW = 'Low',
    MEDIUM = 'Medium',
    HIGH = 'High',
    HIGHEST = 'Highest',
}

export enum FrontLandingPageListingEnum {
    NOT_LISTED = 'Not Listed',
    LOWER_END = 'Lower End',
    TOP = 'Top',
}

export enum RiskOfBeingBlacklistedEnum {
    LOW = 'Low',
    MEDIUM = 'Medium',
    HIGH = 'High',
}
