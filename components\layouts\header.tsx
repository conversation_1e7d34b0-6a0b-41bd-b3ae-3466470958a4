'use client';
import { useDispatch, useSelector } from 'react-redux';
import Link from 'next/link';
import { IRootState } from '@/store';
import { toggleSidebar, toggleRTL } from '@/store/themeConfigSlice';
import Dropdown from '@/components/dropdown';
import IconMenu from '@/components/icon/icon-menu';
import IconLogout from '@/components/icon/icon-logout';
import { NavLogoIcon, NavLogoIconPhone } from '../icon/Icon';
import Profile from '../reusable/HeadeProfile';
import Messages from '../reusable/Messages';

const Header = () => {
    const dispatch = useDispatch();


    return (
        <header className={`z-40 fixed top-0 left-0 right-0 bg-white dark:bg-dark/50 border-b border-white-light dark:border-dark/10 transition-all duration-300`}>
            <div className="shadow-sm">
                <div className="relative flex w-full items-center bg-[#011927] px-5 py-2.5 dark:bg-black">
                    <div className="horizontal-logo flex items-center justify-between ltr:mr-2 rtl:ml-2 md:w-auto w-full">
                        <Link href="/" className=" ">
                            <div className='md:block hidden'>
                                <NavLogoIcon />
                            </div>
                            <div className='md:hidden flex'>
                                <span>

                                <NavLogoIconPhone/>
                                </span>
                        

                            </div>
                          
                        </Link>
                   
                        <button
                            type="button"
                            // className="collapse-icon flex flex-none rounded-full bg-white-light/40 p-2 hover:bg-white-light/90 hover:text-primary ltr:ml-2 rtl:mr-2 dark:bg-dark/40 dark:text-[#d0d2d6] dark:hover:bg-dark/60 dark:hover:text-primary md:hidden"
                            className="collapse-icon  flex-none rounded-full bg-white-light/40 p-2 hover:bg-white-light/90 hover:text-primary ltr:ml-2 rtl:mr-2 dark:bg-dark/40 dark:text-[#d0d2d6] dark:hover:bg-dark/60 dark:hover:text-primary hidden"
                            onClick={() => dispatch(toggleSidebar())}
                        >
                            <IconMenu className="h-5 w-5" />
                        </button>
                    </div>

                    <div className="items-center justify-end space-x-1.5   mr-auto  space-x-reverse  sm:flex-1   lg:space-x-2 md:flex hidden">
                        <div className='flex items-center'>

                            <Messages />
                            <Profile />
                        </div>
                    </div>
                </div>

                {/* horizontal menu */}
            </div>
        </header>
    );
};

export default Header;
