'use client';

import type React from 'react';
import { useRef, useEffect } from 'react';
import { ActionIcon } from '@/components/icon/Icon';

interface ReviewActionsDropdownProps {
    review: any;
    isOpen: boolean;
    onToggle: (reviewId: number, event: React.MouseEvent<HTMLButtonElement>) => void;
    onView: (review: any) => void;
    onHide: (review: any) => void;
    onRestore: (review: any) => void;
    onApprove: (review: any) => void;
    onDelete: (review: any) => void;
    onAddNote: (review: any) => void;
    dropdownPosition: 'top' | 'bottom';
    dropdownRef: React.RefObject<HTMLDivElement>;
    actionButtonRefs: React.MutableRefObject<Map<number, HTMLButtonElement>>;
}

export default function ReviewActionsDropdown({
    review,
    isOpen,
    onToggle,
    onView,
    onHide,
    onRestore,
    onApprove,
    onDelete,
    onAddNote,
    dropdownPosition,
    dropdownRef,
    actionButtonRefs
}: ReviewActionsDropdownProps) {
    const getStatusActions = () => {
        const status = review.statusName;

        switch (status) {
            case 'Published':
                return [
                    { label: 'View Details', action: () => onView(review), icon: '👁️', className: 'text-gray-700 hover:bg-gray-100' },
                    { label: 'Hide Review', action: () => onHide(review), icon: '🚫', className: 'text-yellow-600 hover:bg-yellow-50' },
                    { label: 'Delete', action: () => onDelete(review), icon: '🗑️', className: 'text-red-600 hover:bg-red-50' },
                    { label: 'Add Note', action: () => onAddNote(review), icon: '📝', className: 'text-blue-600 hover:bg-blue-50' }
                ];

            case 'Hidden':
                return [
                    { label: 'View Details', action: () => onView(review), icon: '👁️', className: 'text-gray-700 hover:bg-gray-100' },
                    { label: 'Restore & Publish', action: () => onRestore(review), icon: '✅', className: 'text-green-600 hover:bg-green-50' },
                    { label: 'Add Note', action: () => onAddNote(review), icon: '📝', className: 'text-blue-600 hover:bg-blue-50' }
                ];

            case 'Pending':
            case 'Flagged':
                return [
                    { label: 'View Details', action: () => onView(review), icon: '👁️', className: 'text-gray-700 hover:bg-gray-100' },
                    { label: 'Approve & Publish', action: () => onApprove(review), icon: '✅', className: 'text-green-600 hover:bg-green-50' },
                    { label: 'Hide Review', action: () => onHide(review), icon: '🚫', className: 'text-yellow-600 hover:bg-yellow-50' },
                    { label: 'Delete', action: () => onDelete(review), icon: '🗑️', className: 'text-red-600 hover:bg-red-50' },
                    { label: 'Add Note', action: () => onAddNote(review), icon: '📝', className: 'text-blue-600 hover:bg-blue-50' }
                ];

            case 'Rejected':
                return [
                    { label: 'View Details', action: () => onView(review), icon: '👁️', className: 'text-gray-700 hover:bg-gray-100' },
                    { label: 'Approve & Publish', action: () => onApprove(review), icon: '✅', className: 'text-green-600 hover:bg-green-50' },
                    { label: 'Delete', action: () => onDelete(review), icon: '🗑️', className: 'text-red-600 hover:bg-red-50' },
                    { label: 'Add Note', action: () => onAddNote(review), icon: '📝', className: 'text-blue-600 hover:bg-blue-50' }
                ];

            case 'Archived':
                return [
                    { label: 'View Details', action: () => onView(review), icon: '👁️', className: 'text-gray-700 hover:bg-gray-100' },
                    { label: 'Restore & Publish', action: () => onRestore(review), icon: '✅', className: 'text-green-600 hover:bg-green-50' },
                    { label: 'Delete', action: () => onDelete(review), icon: '🗑️', className: 'text-red-600 hover:bg-red-50' },
                    { label: 'Add Note', action: () => onAddNote(review), icon: '📝', className: 'text-blue-600 hover:bg-blue-50' }
                ];

            default:
                return [
                    { label: 'View Details', action: () => onView(review), icon: '👁️', className: 'text-gray-700 hover:bg-gray-100' },
                    { label: 'Add Note', action: () => onAddNote(review), icon: '📝', className: 'text-blue-600 hover:bg-blue-50' }
                ];
        }
    };

    const actions = getStatusActions();

    return (
        <td className="px-4 py-3 text-right text-sm font-medium relative">
            <button
                ref={el => {
                    if (el) actionButtonRefs.current.set(review.id, el);
                }}
                onClick={(e) => onToggle(review.id, e)}
                className="text-gray-400 hover:text-gray-600 p-1"
                title="Review actions"
            >
                <ActionIcon />
            </button>

            {isOpen && (
                <div
                    ref={dropdownRef}
                    className={`absolute right-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10 ${
                        dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full'
                    }`}
                >
                    <div className="py-1">
                        {actions.map((action, index) => (
                            <button
                                key={index}
                                onClick={action.action}
                                className={`block w-full text-left px-4 py-2 text-sm ${action.className} flex items-center gap-2`}
                            >
                                <span className="text-base">{action.icon}</span>
                                {action.label}
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </td>
    );
}
