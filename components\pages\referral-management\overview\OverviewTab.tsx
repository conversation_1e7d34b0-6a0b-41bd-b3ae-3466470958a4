"use client"

import API_ENDPOINTS from "@/app/lib/apiRoutes"
import React, { useEffect } from "react"
import { Users, FileText, DollarSign, TrendingUp, Clock } from "lucide-react"

// Type definitions
interface DashboardData {
  totalSalespersons: number
  activeSalespersons: number
  totalReferrals: number
  confirmedReferrals: number
  paidCommissionAmount: number
  totalCommissionAmount: number
  conversionRate: number
  avgCommissionPerReferral: number
}

interface TopReferral {
  salesperson_id: string
  salesperson_name: string
  salesperson_referral_code: string
  total_commission: number
  referral_count: number
}

interface RecentReferral {
  referral_id: string
  user_firstname: string
  user_lastname: string
  salesperson_name: string
  referred_date: string
  commissionAmount: number
  commission_status_name: string
}

const OverviewTab = () => {
  const [topReferrals, setTopReferrals] = React.useState<TopReferral[]>([])
  const [recentReferrals, setRecentReferrals] = React.useState<RecentReferral[]>([])
  const [dashboardData, setDashboardData] = React.useState<DashboardData | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  const fetchAllReferrals = async () => {
    try {
      setLoading(true)
      setError(null) 
 
      const [dashboardResponse, topReferralResponse, recentReferralsResponse] = await Promise.all([
        fetch(API_ENDPOINTS.GET_DASHBOARD_REFERRALS, {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }),
        fetch(API_ENDPOINTS.GET_TOP_REFERRALS, {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }),
        fetch(API_ENDPOINTS.GET_RECENT_REFERRALS, {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }),
      ])

      // Check if all responses are successful
      if (!dashboardResponse.ok || !topReferralResponse.ok || !recentReferralsResponse.ok) {
        throw new Error(
          `API Error: ${dashboardResponse.status} ${topReferralResponse.status} ${recentReferralsResponse.status}`,
        )
      }

      const dashboardData = await dashboardResponse.json()
      const topReferralData = await topReferralResponse.json()
      const recentReferralData = await recentReferralsResponse.json()

      setDashboardData(dashboardData.data)
      setTopReferrals(topReferralData.data)
      setRecentReferrals(recentReferralData.data)
    } catch (err) {
      console.error("Error fetching data:", err)
      setError(`Failed to load data:  `)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAllReferrals()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading data: {error}</p>
          <button onClick={fetchAllReferrals} className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            Retry
          </button>
        </div>
      </div>
    )
  }

  // Prepare stats data
  const stats = dashboardData
    ? [
        {
          label: "Total Salespersons",
          value: dashboardData?.totalSalespersons,
          subtext: `${dashboardData?.activeSalespersons} active`,
          icon: Users,
        },
        {
          label: "Total Referrals",
          value: dashboardData?.totalReferrals,
          subtext: `${dashboardData?.confirmedReferrals} confirmed`,
          icon: FileText,
        },
        {
          label: "Commissions Paid",
          value: `AED ${dashboardData?.paidCommissionAmount?.toLocaleString()}`,
          subtext: `AED ${(dashboardData?.totalCommissionAmount - dashboardData?.paidCommissionAmount).toLocaleString()} pending`,
          icon: DollarSign,
        },
        {
          label: "Conversion Rate",
          value: `${dashboardData?.conversionRate ?dashboardData?.conversionRate :"0"}`,
          subtext: `AED ${dashboardData?.avgCommissionPerReferral?dashboardData?.avgCommissionPerReferral :"0" } avg commission`,
          icon: TrendingUp,
        },
      ]
    : []

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  const getStatusBadge = (status: string) => {
    const statusLower = status.toLowerCase()
    if (statusLower === "confirmed") {
      return "bg-green-100 text-green-800"
    } else if (statusLower === "pending") {
      return "bg-blue-100 text-blue-800"
    } else if (statusLower === "paid") {
      return "bg-red-100 text-red-800"
    } else {
      return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => {
          const IconComponent = stat.icon
          return (
            <div key={index} className="rounded-lg bg-white p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium text-[#636363]">{stat.label}</div>
                <IconComponent className="h-4 w-4 text-[#636363]" />
              </div>
              <div className="text-3xl font-semibold text-[#2d2d2e] mb-1">{stat.value}</div>
              <div className="text-sm text-[#636363]">{stat.subtext}</div>
            </div>
          )
        })}
      </div>

      {/* Top Performers Section */}
      <div className="rounded-lg bg-white p-6 shadow-sm border border-gray-100">
        <h2 className="mb-6 flex items-center gap-2 text-lg font-semibold text-[#2d2d2e]">
          <Users className="h-5 w-5" />
          Top Performers
        </h2>
        <div className="space-y-4">
          {topReferrals.length > 0 ? (
            topReferrals.map((performer, index) => (
              <div
                key={performer?.salesperson_id}
                className="flex items-center justify-between rounded-lg bg-gray-50 p-4"
              >
                <div className="flex items-center gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-sm font-medium text-blue-800">
                    #{index + 1}
                  </div>
                  <div>
                    <div className="font-medium text-[#2d2d2e]">{performer?.salesperson_name}</div>
                    <div className="text-sm text-[#636363]">ID: {performer?.salesperson_referral_code}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-[#2d2d2e]">AED {performer?.total_commission}</div>
                  <div className="text-sm text-[#636363]">{performer?.referral_count} referrals</div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">No top performers data available</div>
          )}
        </div>
      </div>

      {/* Recent Referrals Section */}
      <div className="rounded-lg bg-white p-6 shadow-sm border border-gray-100">
        <h2 className="mb-6 flex items-center gap-2 text-lg font-semibold text-[#2d2d2e]">
          <Clock className="h-5 w-5" />
          Recent Referrals
        </h2>
        <div className="space-y-4">
          {recentReferrals.length > 0 ? (
            recentReferrals.map((referral) => (
              <div key={referral?.referral_id} className="flex items-center justify-between rounded-lg bg-gray-50 p-4">
                <div>
                  <div className="font-medium text-[#2d2d2e]">
                    {referral?.user_firstname} {referral?.user_lastname}
                  </div>
                  <div className="text-sm text-[#636363]">
                    Referred by <span className="text-[#1D7EB6]">{referral?.salesperson_name}</span> •
                    <span className="ml-1">{formatDate(referral?.referred_date)}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-[#2d2d2e]">AED {referral?.commissionAmount}</div>
                  <div
                    className={`inline-block rounded-full px-2 py-1 text-xs ${getStatusBadge(referral?.commission_status_name)}`}
                  >
                    {referral?.commission_status_name }
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">No recent referrals available</div>
          )}
        </div>
      </div>
    </div>
  )
}

export default OverviewTab
