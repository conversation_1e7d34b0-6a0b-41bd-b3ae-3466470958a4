"use client";
import React, { useState } from "react";
import Select from "react-select";

interface TimeOption {
  label: string;
  value: string;
}

interface TimeDropdownProps {
  data: TimeOption[];
  label: string;
  selectedTime: string;
  onTimeChange?: (selected: TimeOption | null) => void; 
  minTime?: string;
  required?: boolean;
}

export const TimeDropdown = ({ data, label, selectedTime, onTimeChange, minTime, required }: TimeDropdownProps) => {
  const [selectedValue, setSelectedValue] = useState<TimeOption | null>(
    data.find((option) => option.value === selectedTime) || null
  );

  const handleChange = (selected: TimeOption | null) => {
    setSelectedValue(selected);
    if (onTimeChange) {
      onTimeChange(selected); // Only pass `TimeOption | null`
    }
  };  

  const colourStyles = {
    control: (styles: any) => ({
      ...styles,
      width: "100%",
      backgroundColor: "transparent",
      borderRadius: "4px",
      // borderColor: "#1D7EB6",
      boxShadow: "none",
      fontSize: "14px",
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      padding: "4px",
    }),
    menu: (styles: any) => ({
      ...styles,
      backgroundColor: "#F1F6F5",
      borderRadius: "4px",
      zIndex: 1000,
    }),
    option: (styles: any, { isSelected }: { isSelected: boolean }) => ({
      ...styles,
      backgroundColor: isSelected ? "#cacace" : "#EEEDF4",
      ":hover": { backgroundColor: "#cacace" },
    }),
  };

  return (
    <div style={{ position: "relative", width: "100%" }}>
      <label
        className="text-black font-normal"
        style={{
          position: "absolute",
          top: selectedValue ? "-8px" : "50%",
          left: "12px",
          backgroundColor: "white",
          fontSize: selectedValue ? "12px" : "14px",
          padding: "0 4px",
          transition: "all 0.2s ease-in-out",
          transform: selectedValue ? "translateY(0)" : "translateY(-50%)",
          pointerEvents: "none",
          zIndex: 10,
        }}
      >
        {label}
      </label>

      <Select
        options={data}
        placeholder=""
        styles={colourStyles}
        onChange={handleChange}
        value={selectedValue}
        isSearchable={false}
      />
    </div>
  );
};
