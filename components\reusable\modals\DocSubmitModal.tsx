import React from 'react';
import Link from 'next/link';
import { ButtonBorder } from '../ButtonBorder';
import { SuccessIconIcon } from '@/components/icon/Icon';

interface ModalProps {
    isOpen: boolean;
    onClose: () => void;
}

export default function DocSubmitModal({ isOpen, onClose }: ModalProps) {
    if (!isOpen) return null;

    // Close modal if user clicks outside the modal content
    const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        if (event.target === event.currentTarget) {
            onClose();
        }
    };

    return (
        <div
            className="fixed inset-0 !z-[999999] flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm"
            onClick={handleBackdropClick} // Detect clicks outside modal
        >
            <div className="max-w-md flex flex-col gap-4 rounded-lg bg-white p-8 text-center shadow-lg">
                <div className="flex justify-center">
                    <SuccessIconIcon className="h-32 w-32" />
                </div>

                <h2 className="text-redMain text-[26px] font-semibold">Documents Submitted</h2>

                <div className="flex flex-col gap-3 text-[#636363] text-base font-normal">
                    <p>
                        We are reviewing the information you provided and will notify you via email within 2 days. You can also check your account status through the dashboard.
                    </p>

                    <p className="text-grayText">
                        For further assistance, please contact our{' '}
                        <Link href="#" className="text-blueMain font-semibold">
                            customer support team
                        </Link>
                        .
                    </p>
                </div>

                <ButtonBorder className="h-12" invertColors={true} onClick={onClose} value="Continue to Dashboard" />
            </div>
        </div>
    );
}