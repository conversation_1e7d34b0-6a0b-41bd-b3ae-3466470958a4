'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import React from 'react';
import { useRouter } from 'next/navigation';
import AgentsTable from './AgentsTable';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';

const AgentsTableLayout = () => {
    const { push } = useRouter();

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Agent Applications"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Agent Applications' }]}
                // ButonComponent={
                //     <BreadCrumButton
                //         onClick={() => {
                //             push('/advertising');
                //         }}
                //     />
                // }
            />
            <div className="px-4">
                <AgentsTable />
            </div>
        </DefaultPageLayout>
    );
};

export default AgentsTableLayout;
