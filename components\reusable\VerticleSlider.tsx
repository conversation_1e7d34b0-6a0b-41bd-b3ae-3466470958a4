"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/effect-coverflow";
import "swiper/css/navigation";
import "swiper/css/pagination";

import React, { useRef, useState } from "react";
// Import Swiper React components

// Import Swiper styles
import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/navigation";
import "swiper/css/thumbs";
 
import Images from '@/public/assets/images/main/Preview-image.png';
// import required modules
import { FreeMode, Navigation, Thumbs } from "swiper/modules";
import Image from "next/image";

export default function VerticalSlider() {
  const [thumbsSwiper, setThumbsSwiper] = useState(null);

  return (
    <div className="flex md:flex-row flex-col gap-5 pt-5">
      <div className="2xl:w-[80%] md:w-[75%] w-full h-fit">
        <Swiper
          spaceBetween={10}
          thumbs={{ swiper: thumbsSwiper }}
          modules={[FreeMode, Navigation, Thumbs]}
          className="mySwiper2 h-fit"
        >
          {Array.from({ length: 10 }).map((_, index) => (
            <SwiperSlide
              key={index}
              className="md:min-h-[598px] md:max-h-[598px] max-h-96 w-full"
            >
              <Image
                key={index}
                width={800}
                height={800}
                alt={`${index}`}
                src={Images}
                className="rounded-[15px] w-full min-h-[598px] object-cover max-h-[598px] "
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      <div className="md:block hidden 2xl:w-[20%] xl:w-[25%] w-[18%] md:max-h-[598px] max-h-96">
        <Swiper
          onSwiper={setThumbsSwiper as any}
          spaceBetween={20}
          slidesPerView={4}
          freeMode={true}
          watchSlidesProgress={true}
          direction="vertical"
          pagination={{
            clickable: true,
          }}
          modules={[FreeMode, Navigation, Thumbs]}
          className="mySwiper !p-0 md:max-h-[598px] max-h-96 mb-2"
        >
          {Array.from({ length: 10 }).map((image, index) => (
            <SwiperSlide
              key={index}
              className="2xl:min-w-[254px] min-h-[186px]  max-h-[186px] !w-full"
            >
              <Image
                width={900}
                height={800}
                key={index}
                src={Images}
                alt={`Thumbnail ${index + 1}`}
                className=" min-h-[186px] max-h-[186px] w-full object-cover rounded-[14px]"
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      <div className="md:hidden block w-full md:max-h-[598px]">
        <Swiper
          onSwiper={setThumbsSwiper as any}
          spaceBetween={20}
          slidesPerView={3}
          freeMode={true}
          watchSlidesProgress={true}
          pagination={{
            clickable: true,
          }}
          modules={[FreeMode, Navigation, Thumbs]}
          className="mySwiper !p-0 md:max-h-[598px] mb-2"
        >
          {Array.from({ length: 10 }).map((image, index) => (
            <SwiperSlide
              key={index}
              className="w-1/2 min-h-[120px] max-h-[120px]"
            >
              <Image
                width={900}
                height={800}
                key={index}
                src={Images}
                alt={`Thumbnail ${index + 1}`}
                className=" min-h-[120px] max-h-[120px] w-full object-cover rounded-[14px]"
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
