import React from 'react';
import Link from 'next/link';
import { ButtonBorder } from '../ButtonBorder';
import { CalendarIcon, SuccessIconIcon } from '@/components/icon/Icon';
import DateInput from '../DateInput';

import { TimeDropdown } from '../TimeDropdown';
import LabelInputField from '../LabelInputField';
import LabelTextArea from '../LabelTextArea';
import IconX from '@/components/icon/icon-x';

interface ModalProps {
 
    onClose: () => void;
}

export default function CreateEventModel({  onClose }: ModalProps) {
    

    // Close modal if user clicks outside the modal content
    const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        if (event.target === event.currentTarget) {
            onClose();
        }
    };
    const timeOptions = Array.from({ length: 12 }, (_, i) => ({
        label: `${i + 12}:00 ${i === 0 ? 'PM' : i === 11 ? 'AM' : 'PM'}`,
        value: `${i + 12}:00 ${i === 0 ? 'PM' : i === 11 ? 'AM' : 'PM'}`,
    }));
    const timeOptions2 = [
        {
            label: `15 minutes before `,
            value: `15 minutes before `,
        },
        {
            label: `30 minutes before `,
            value: `30 minutes before `,
        },
        {
            label: `1 hour before `,
            value: `1 hour before `,
        },
    ];
    return (
        <div
            className="w-full  "
            onClick={handleBackdropClick} // Detect clicks outside modal
        >
            <div className="   flex-col gap-4    ">
                <div className="flex justify-end">
                    <button type="button" className="    text-gray-400 outline-none ltr:right-4 rtl:left-4" onClick={() => onClose()}>
                        <IconX />
                    </button>
                </div>

                <div className="justify-start font-inter">
                    <span className="text-xl font-semibold text-orange-800  ">New </span>
                    <span className="text-xl font-semibold text-zinc-800  ">Event</span>
                </div>

                <div className="  bg-white pb-2">
                    <div className="flex flex-col gap-4 pb-3 pt-1 md:flex-row md:gap-0">
                        <div className="flex items-center gap-3 md:w-1/2">
                            <p className="text-grayMain pb-1 text-sm font-semibold">Color</p>
                            <label className="inline-flex cursor-pointer items-center">
                                <input
                                    type="radio"
                                    className="form-radio"
                                    name="type"
                                    value="primary"
                                    // checked={params.type === 'primary'}
                                    // onChange={(e) => setParams({ ...params, type: e.target.value })}
                                />
                                <span className="text-grayMain text-sm font-normal">Blue</span>
                            </label>
                            <label className="inline-flex cursor-pointer items-center">
                                <input
                                    type="radio"
                                    className="form-radio text-danger"
                                    name="type"
                                    value="danger"
                                    // checked={params.type === 'danger'}
                                    // onChange={(e) => setParams({ ...params, type: e.target.value })}
                                />
                                <span className="text-grayMain text-sm font-normal">Red</span>
                            </label>
                        </div>
                        <div className="flex items-center gap-3 md:w-1/2">
                            <p className="text-grayMain pb-1 text-sm font-semibold">Type</p>
                            <label className="inline-flex cursor-pointer items-center">
                                <input
                                    type="radio"
                                    className="form-radio"
                                    name="type"
                                    // value="primary"
                                    // checked={params.type === 'primary'}
                                    // onChange={(e) => setParams({ ...params, type: e.target.value })}
                                />
                                <span className="text-grayMain text-sm font-normal">Onsite</span>
                            </label>
                            <label className="inline-flex cursor-pointer items-center">
                                <input
                                    type="radio"
                                    className="form-radio text-danger"
                                    name="type"
                                    // value="danger"
                                    // checked={params.type === 'danger'}
                                    // onChange={(e) => setParams({ ...params, type: e.target.value })}
                                />
                                <span className="text-grayMain text-sm font-normal">Offsite</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <div className="max-w-96   flex justify-start">
                            <div className="w-60">
                                <TimeDropdown
                                    data={timeOptions2}
                                    label="Remind Me"
                                    selectedTime={''}
                                    // onTimeChange={(selected) => changeValue({ target: { value: selected?.value || "" } } as any)}
                                    minTime={''}
                                    required
                                />
                            </div>
                        </div>
                    </div>
                    <div className="flex w-full items-center gap-3 py-2 md:flex-row md:gap-0">
                        <div className="  min-w-96 w-full md:pr-1">
                            <DateInput placeholder="Add a Date*" icon={<CalendarIcon />} />
                        </div>

                        <div className="min-w-96 w-full md:px-1">
                            <TimeDropdown
                                data={timeOptions}
                                label="Start Time"
                                selectedTime={''}
                                // onTimeChange={(selected) => startDateChange({ target: { value: selected?.value || "" } } as any)}
                                minTime={''}
                                required
                            />
                        </div>

                        <span className="px-3">to</span>

                        <div className="min-w-96 w-full md:pl-1">
                            <TimeDropdown
                                data={timeOptions}
                                label="End Time"
                                selectedTime={''}
                                // onTimeChange={(selected) => changeValue({ target: { value: selected?.value || "" } } as any)}
                                minTime={''}
                                required
                            />
                        </div>
                    </div>

                    <div className="space-y-5 pt-3">
                        <div className="">
                            <LabelInputField id="title" label="Add a Title*" value={''} onChange={() => {}} />
                        </div>

                        <div className="">
                            <LabelInputField
                                id="invite-person"
                                label="Invite Person*"
                                // value={params.title || ""}
                                // onChange={(e) => changeValue(e)}
                            />
                        </div>

                        <div className="">
                            <LabelInputField
                                id="search-location"
                                label="Search Location"
                                // value={params.title || ""}
                                // onChange={(e) => changeValue(e)}
                            />
                        </div>
                        <div>
                            <LabelTextArea id="description" label="Description" value={''} onChange={() => {}} />
                        </div>

                        <div className="flex items-center justify-end gap-3">
                            <button type="button" className="text-sm text-blueMain" onClick={() => {}}>
                                Cancel
                            </button>
                            <div>
                                <ButtonBorder className="" value="Save as Draft" />
                            </div>
                            <div>
                                <ButtonBorder
                                    onClick={() => {
                                        // saveEvent();
                                    }}
                                    className="!bg-blueMain !text-white"
                                    value={'Create Event'}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
