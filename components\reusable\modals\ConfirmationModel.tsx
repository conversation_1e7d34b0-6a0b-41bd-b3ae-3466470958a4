'use client';
import { Dialog, Transition } from '@headlessui/react';
import { useState, Fragment, use } from 'react';
import ThanksIcons from '@/../public/assets/images/Delete.svg';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { CloseIconModel, DeleteMessageDashboradIcon } from '@/components/icon/Icon';

const ConfirmationModel = ({ onClose  ,
    onSuccessfulDelete,
    heading , details,showImage,actionButtonText
}: any) => {
    const [modal2, setModal2] = useState(false);
    const router = useRouter();
    const handleModal = () => {
        setModal2(true);
    };

    const handleDelete = (e: any) => {
        onClose(true);
        setModal2(false);
    };
    return (
        <>
         

            <div className=" w-full">
                <div className=" relative flex items-center justify-between ">
                    <div className=" relative flex items-start justify-start "></div>
                    <div className=" relative flex items-end justify-end ">
                        <span className="cursor-pointer   pt-5" onClick={handleDelete}>
                            <CloseIconModel />
                        </span>
                    </div>
                </div>
                <div className="h-100 flex flex-col justify-between">
                    <div className=" ">
                       {showImage === false ? "": <Image src={ThanksIcons} alt="Success" width={200} height={200} className="m-auto rounded-full" />}
                        <div className=" py-2 text-center">
                            <p   className="font-inter text-[26px] font-semibold  text-[#993333]">
                       {heading ?heading:     "Delete Lorem Ipsum!"}
                            </p>
                            <p className="self-stretch  pt-5 text-center justify-start text-[#636363] text-base font-normal font-inter leading-normal">Make sure this action can’t be undone.</p>
                        </div>
                           <div className="grid grid-cols-2 gap-4 pt-5 max-md:grid-cols-1">
                            <button onClick={handleDelete} type="submit" className="flex items-center justify-center rounded-lg bg-[#1D7EB6] px-6 py-4 text-lg font-medium text-white">
                                Cancel
                            </button>
                            <button onClick={onSuccessfulDelete} className="flex items-center justify-center  rounded-lg border border-[#1D7EB6] px-6 py-4 text-lg font-medium text-[#1D7EB6]">
                          {actionButtonText ?actionButtonText :      "Delete"}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default ConfirmationModel;
