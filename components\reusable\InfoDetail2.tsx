import Image from 'next/image';
import { CopyIcon, HeartIcon, InfoIcon, TypeIcon, MessageIcon, ReviewStarIcon } from '../icon/Icon';
import StatusBadge from './StatusBandage';
import { useToast } from './Notify';
import { useEffect, useState } from 'react';
import React from 'react';
import Dropdown from '../dropdown';
import SearchDropDown from './SearchDropDown';

type InfoField = {
    label: string;
    value: string;
    no?: boolean;
    link?: string;
    info?: boolean;
    tag?: boolean;
    badges?: boolean;
    reviews?: boolean;
    editable?: boolean;
    type?: 'text' | 'email' | 'number' | 'date' | 'textarea';
    name?: string;
};

type InfoDetailProps = {
    infoData: InfoField[];
    title: string;
    copyLink?: string;
    editable?: boolean;
    onDataChange?: (data: InfoField[]) => void;
};

export const InfoDetail2 = ({ infoData, title, copyLink, editable = false, onDataChange }: InfoDetailProps) => {
    const { showToast } = useToast();
    const [editableData, setEditableData] = useState<InfoField[]>(infoData);

    const handleInputChange = (index: number, value: string) => {
        const newData = [...editableData];
        newData[index] = { ...newData[index], value };
        setEditableData(newData);

        // Call the optional callback if provided
        if (onDataChange) {
            onDataChange(newData);
        }
    };

    useEffect(() => {
        setEditableData(infoData);
    }, [infoData]);
    const dropdown = [{ label: 'Null' }, { label: 'Converted' }, { label: 'Not Converted' }, { label: 'In Progress' }];

    return (
        <div className="flex w-full flex-col overflow-hidden rounded-lg border border-[#e4e4e4] font-inter">
            <div className="flex h-[60px] w-full items-center justify-between bg-[#e4e4e4] px-4">
                <span className="text-base font-medium text-[#2d2d2e] md:text-xl">{title}</span>
                {copyLink && (
                    <span onClick={() => showToast('Link copied! You can now paste it anywhere.', 'success', 5000)} className="cursor-pointer font-inter text-sm font-medium text-[#1D7EB6]">
                        {copyLink}
                    </span>
                )}
            </div>
            <div className="overflow-y-auto">
                {editableData.map((field, index) => (
                    <div key={index} className="flex border-t border-[#e4e4e4] bg-white">
                        {!field.no ? (
                            <>
                                <div className="flex w-1/3 items-center bg-neutral-100 py-2 pl-2 pr-2 md:py-3 md:pl-4">
                                    <span className="w-full break-words text-sm font-normal text-[#2d2d2e] md:text-base">{field.label}</span>
                                </div>

                                <div className="flex w-2/3 items-center p-4 py-3 text-sm font-normal text-[#636363] md:text-base">
                                    {field.label === 'Status' ? (
                                        <StatusBadge status={field.value} />
                                    ) : editable && field.editable !== false ? (
                                        field.type === 'textarea' ? (
                                            <textarea
                                                value={field.value}
                                                placeholder={`Enter ${field.label.toLowerCase()}`}
                                                onChange={(e) => handleInputChange(index, e.target.value)}
                                                className="w-full rounded focus:outline-none"
                                                rows={3}
                                            />
                                        ) : (
                                            <input
                                                type={
                                                    field.type ||
                                                    (field.label === 'Email'
                                                        ? 'email'
                                                        : field.label.includes('Date')
                                                        ? 'date'
                                                        : field.label.includes('Phone') ||
                                                          field.label.includes('Contact') ||
                                                          field.label.includes('Number') ||
                                                          ['Call/SMS', 'WhatsApp'].includes(field.label)
                                                        ? 'number'
                                                        : 'text')
                                                }
                                                value={field.value}
                                                name={field.name}
                                                placeholder={`Enter ${field.label.toLowerCase()}`}
                                                onChange={(e) => handleInputChange(index, e.target.value)}
                                                className="w-full rounded focus:outline-none"
                                            />
                                        )
                                    ) : (
                                        <>
                                            {field.reviews && (
                                                <span className="flex flex-wrap items-center justify-start gap-2 pe-2">
                                                    <ReviewStarIcon />
                                                </span>
                                            )}
                                            {field.value ? field.value : <span className="text-[#959595]">{`Enter ${field.label.toLowerCase()}`}</span>}
                                        </>
                                    )}

                                    {field.label === 'Campain ID' && (
                                        <span className="cursor-pointer ps-2">
                                            <CopyIcon />
                                        </span>
                                    )}

                                    {field.link && (
                                        <span className="h-32 w-32 cursor-pointer rounded-lg border p-2">
                                            <Image src={field.link} alt="Picture of the author" className="h-full w-full rounded-lg object-cover" width={1000} height={1000} />
                                        </span>
                                    )}

                                    {field.info && (
                                        <span className="flex w-full items-center justify-between gap-2">
                                            <StatusBadge status={field.value} />
                                            <InfoIcon />
                                        </span>
                                    )}

                                    {field.tag && (
                                        <span className="flex w-full flex-wrap items-center justify-start gap-2">
                                            {[3, 3, 4, 4].map((_, tagIndex) => (
                                                <div
                                                    key={tagIndex}
                                                    className="inline-flex h-[26px] items-center justify-center gap-2.5 rounded border border-[#959595] bg-neutral-100 p-2 text-center text-sm font-medium text-[#959595]"
                                                >
                                                    Tag {tagIndex + 1}
                                                </div>
                                            ))}
                                        </span>
                                    )}

                                    {field.badges && (
                                        <span className="flex w-full flex-wrap items-center justify-start gap-2">
                                            <TypeIcon />
                                            <HeartIcon />
                                        </span>
                                    )}
                                </div>
                            </>
                        ) : (
                            <div className="flex w-full items-start bg-white py-2 pl-2 pr-2 md:py-3 md:pl-4">
                                <span className="w-full break-words text-sm font-medium text-[#2D2D2E] md:text-base">{field.label}</span>
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

// New component for additional table data with editable fields
export const EditableTable = ({ infoData, title, copyLink, editable = true, onDataChange }: InfoDetailProps) => {
    const { showToast } = useToast();
    const [editableData, setEditableData] = useState<InfoField[]>(infoData);

    const handleInputChange = (index: number, value: string) => {
        const newData = [...editableData];
        newData[index] = { ...newData[index], value };
        setEditableData(newData);

        // Call the optional callback if provided
        if (onDataChange) {
            onDataChange(newData);
        }
    };

    const handleSave = () => {
        if (onDataChange) {
            onDataChange(editableData);
        }
        showToast('Data saved successfully!', 'success', 3000);
    };

    return (
        <div className="flex w-full flex-col overflow-hidden rounded-lg border border-[#e4e4e4] font-inter">
            <div className="flex h-[60px] w-full items-center justify-between bg-[#e4e4e4] px-4">
                <span className="text-base font-medium text-[#2d2d2e] md:text-xl">{title}</span>
                {copyLink && (
                    <span onClick={() => showToast('Link copied! You can now paste it anywhere.', 'success', 5000)} className="cursor-pointer font-inter text-sm font-medium text-[#1D7EB6]">
                        {copyLink}
                    </span>
                )}
            </div>
            <div className="overflow-y-auto">
                {editableData.map((field, index) => (
                    <div key={index} className="flex border-t border-[#e4e4e4] bg-white">
                        {!field.no ? (
                            <>
                                <div className="flex w-1/3 items-center bg-neutral-100 py-2 pl-2 pr-2 md:py-3 md:pl-4">
                                    <span className="w-full break-words text-sm font-normal text-[#2d2d2e] md:text-base">{field.label}</span>
                                </div>

                                <div className="flex w-2/3 items-center p-4 py-3 text-sm font-normal text-[#636363] md:text-base">
                                    {field.label === 'Status' ? (
                                        <StatusBadge status={field.value} />
                                    ) : editable && field.editable !== false ? (
                                        field.type === 'textarea' ? (
                                            <textarea
                                                value={field.value}
                                                placeholder={`Enter ${field.label.toLowerCase()}`}
                                                onChange={(e) => handleInputChange(index, e.target.value)}
                                                className="w-full rounded px-3 py-2 focus:outline-none"
                                                rows={3}
                                            />
                                        ) : (
                                            <input
                                                type={
                                                    field.type ||
                                                    (field.label === 'Email'
                                                        ? 'email'
                                                        : field.label.includes('Date')
                                                        ? 'date'
                                                        : field.label.includes('Phone') || field.label.includes('Number') || ['Call/SMS', 'WhatsApp'].includes(field.label)
                                                        ? 'number'
                                                        : 'text')
                                                }
                                                value={field.value}
                                                placeholder={`Enter ${field.label.toLowerCase()}`}
                                                onChange={(e) => handleInputChange(index, e.target.value)}
                                                className="w-full rounded px-3 py-2 focus:outline-none"
                                            />
                                        )
                                    ) : (
                                        <>
                                            {field.reviews && (
                                                <span className="flex flex-wrap items-center justify-start gap-2 pe-2">
                                                    <ReviewStarIcon />
                                                </span>
                                            )}
                                            {field.value}
                                        </>
                                    )}

                                    {field.info && (
                                        <span className="flex w-full items-center justify-between gap-2">
                                            <StatusBadge status={field.value} />
                                            <InfoIcon />
                                        </span>
                                    )}

                                    {field.link && (
                                        <span className="h-32 w-32 cursor-pointer rounded-lg border p-2">
                                            <Image src={field.link} alt="Picture of the author" className="h-full w-full rounded-lg object-cover" width={1000} height={1000} />
                                        </span>
                                    )}
                                </div>
                            </>
                        ) : (
                            <div className="flex w-full items-start bg-white py-2 pl-2 pr-2 md:py-3 md:pl-4">
                                <span className="w-full break-words text-sm font-medium text-[#2D2D2E] md:text-base">{field.label}</span>
                            </div>
                        )}
                    </div>
                ))}
            </div>
            {editable && (
                <div className="flex justify-end gap-2 border-t border-[#e4e4e4] p-4">
                    <button onClick={handleSave} className="rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none">
                        Save Changes
                    </button>
                </div>
            )}
        </div>
    );
};

// New component for Project Details with editable fields
export const ProjectDetailsTable = ({ infoData, title = 'Project Details', copyLink, editable = true, onDataChange }: InfoDetailProps) => {
    const { showToast } = useToast();
    const [editableData, setEditableData] = useState<InfoField[]>(infoData);

    const handleInputChange = (index: number, value: string) => {
        const newData = [...editableData];
        newData[index] = { ...newData[index], value };
        setEditableData(newData);

        if (onDataChange) {
            onDataChange(newData);
        }
    };

    const handleSave = () => {
        if (onDataChange) {
            onDataChange(editableData);
        }
        showToast('Project details saved successfully!', 'success', 3000);
    };

    // Common input field styling
    const inputClass = 'w-full rounded px-3 py-2 focus:outline-none';

    return (
        <div className="flex w-full flex-col overflow-hidden rounded-lg border border-[#e4e4e4] font-inter">
            <div className="flex h-[60px] w-full items-center justify-between bg-[#e4e4e4] px-4">
                <span className="text-base font-medium text-[#2d2d2e] md:text-xl">{title}</span>
                {copyLink && (
                    <span onClick={() => showToast('Link copied! You can now paste it anywhere.', 'success', 5000)} className="cursor-pointer font-inter text-sm font-medium text-[#1D7EB6]">
                        {copyLink}
                    </span>
                )}
            </div>
            <div className="overflow-y-auto">
                {editableData.map((field, index) => (
                    <div key={index} className="flex border-t border-[#e4e4e4] bg-white">
                        {!field.no ? (
                            <>
                                <div className="flex w-1/3 items-center bg-neutral-100 py-2 pl-2 pr-2 md:py-3 md:pl-4">
                                    <span className="w-full break-words text-sm font-normal text-[#2d2d2e] md:text-base">{field.label}</span>
                                </div>

                                <div className="flex w-2/3 items-center p-4 py-3 text-sm font-normal text-[#636363] md:text-base">
                                    {field.label === 'Status' ? (
                                        editable && field.editable !== false ? (
                                            <select value={field.value} onChange={(e) => handleInputChange(index, e.target.value)} className={inputClass}>
                                                <option value="Active">Active</option>
                                                <option value="Pending">Pending</option>
                                                <option value="Completed">Completed</option>
                                                <option value="On Hold">On Hold</option>
                                                <option value="Cancelled">Cancelled</option>
                                            </select>
                                        ) : (
                                            <StatusBadge status={field.value} />
                                        )
                                    ) : field.label === 'Priority' ? (
                                        editable && field.editable !== false ? (
                                            <select value={field.value} onChange={(e) => handleInputChange(index, e.target.value)} className={inputClass}>
                                                <option value="High">High</option>
                                                <option value="Medium">Medium</option>
                                                <option value="Low">Low</option>
                                            </select>
                                        ) : (
                                            <span className={`rounded px-2 py-1 text-white ${field.value === 'High' ? 'bg-red-500' : field.value === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'}`}>
                                                {field.value}
                                            </span>
                                        )
                                    ) : field.label === 'Tags' || field.tag ? (
                                        editable && field.editable !== false ? (
                                            <input
                                                type="text"
                                                value={field.value}
                                                placeholder="Enter tags (comma separated)"
                                                onChange={(e) => handleInputChange(index, e.target.value)}
                                                className={inputClass}
                                            />
                                        ) : (
                                            <span className="flex w-full flex-wrap items-center justify-start gap-2">
                                                {field.value.split(',').map((tag, tagIndex) => (
                                                    <div
                                                        key={tagIndex}
                                                        className="inline-flex h-[26px] items-center justify-center gap-2.5 rounded border border-[#959595] bg-neutral-100 p-2 text-center text-sm font-medium text-[#959595]"
                                                    >
                                                        {tag.trim()}
                                                    </div>
                                                ))}
                                            </span>
                                        )
                                    ) : editable && field.editable !== false ? (
                                        field.type === 'textarea' ? (
                                            <textarea
                                                value={field.value}
                                                placeholder={`Enter ${field.label.toLowerCase()}`}
                                                onChange={(e) => handleInputChange(index, e.target.value)}
                                                className={inputClass}
                                                rows={3}
                                            />
                                        ) : field.type === 'date' ? (
                                            <input type="date" value={field.value} onChange={(e) => handleInputChange(index, e.target.value)} className={inputClass} />
                                        ) : (
                                            <input
                                                type={
                                                    field.type ||
                                                    (field.label === 'Email'
                                                        ? 'email'
                                                        : field.label.includes('Date')
                                                        ? 'date'
                                                        : field.label.includes('Budget') || field.label.includes('Cost')
                                                        ? 'number'
                                                        : 'text')
                                                }
                                                value={field.value}
                                                placeholder={`Enter ${field.label.toLowerCase()}`}
                                                onChange={(e) => handleInputChange(index, e.target.value)}
                                                className={inputClass}
                                            />
                                        )
                                    ) : (
                                        <>
                                            {field.reviews && (
                                                <span className="flex flex-wrap items-center justify-start gap-2 pe-2">
                                                    <ReviewStarIcon />
                                                </span>
                                            )}
                                            {field.value}
                                        </>
                                    )}

                                    {field.info && (
                                        <span className="flex w-full items-center justify-between gap-2">
                                            <StatusBadge status={field.value} />
                                            <InfoIcon />
                                        </span>
                                    )}

                                    {field.link && (
                                        <span className="h-32 w-32 cursor-pointer rounded-lg border p-2">
                                            <Image src={field.link} alt="Picture of the author" className="h-full w-full rounded-lg object-cover" width={1000} height={1000} />
                                        </span>
                                    )}
                                </div>
                            </>
                        ) : (
                            <div className="flex w-full items-start bg-white py-2 pl-2 pr-2 md:py-3 md:pl-4">
                                <span className="w-full break-words text-sm font-medium text-[#2D2D2E] md:text-base">{field.label}</span>
                            </div>
                        )}
                    </div>
                ))}
            </div>
            {editable && (
                <div className="flex justify-end gap-2 border-t border-[#e4e4e4] p-4">
                    <button onClick={handleSave} className="rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none">
                        Save Project Details
                    </button>
                </div>
            )}
        </div>
    );
};