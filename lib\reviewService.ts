import { REVIEWS_API, REVIEWS_STATS_API, REVIEW_UPDATE_STATUS_API, REVIEW_DELETE_API, REVIEW_ADD_NOTE_API, REVIEW_RESTORE_API, REVIEW_FLAG_API, REVIEW_HIDDEN_API } from '@/app/lib/apiRoutes';

interface Note {
    id: number;
    note: string;
    createdAt: string;
    adminName: string;
}

interface HistoryEntry {
    id: number;
    action: string;
    previousStatus: number;
    newStatus: number;
    previousStatusName: string;
    newStatusName: string;
    notes: string | null;
    createdAt: string;
    adminName: string;
}

interface Review {
    id: number;
    reviewerId: number;
    revieweeId: number;
    reviewText: string;
    rating: number;
    statusId: number;
    hideReason: string | null;
    flagged: boolean;
    created_at: string;
    updated_at: string;
    reviewerFirstName: string | null;
    reviewerLastName: string | null;
    reviewerEmail: string;
    revieweeFirstName: string | null;
    revieweeLastName: string | null;
    revieweeEmail: string;
    revieweeType: string;
    agencyName: string | null;
    statusName: string;
    notes: Note[];
    history: HistoryEntry[];
}

interface ReviewsResponse {
    status: number;
    success: boolean;
    message: string;
    data: {
        reviews: Review[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    };
}

interface ApiResponse {
    status: number;
    success: boolean;
    message: string;
    data?: any;
}

interface ReviewStats {
    totalReviews: number;
    pendingReviews: number;
    approvedReviews: number;
    rejectedReviews: number;
    averageRating: number;
    hiddenReviews: number;
    flaggedReviews: number;
    // Optional fields that might be in API response
    total?: number;
    published?: number;
    pending?: number;
    rejected?: number;
    hidden?: number;
    flagged?: number;
}

interface ReviewStatsResponse {
    status: number;
    success: boolean;
    message: string;
    data: ReviewStats;
}

class ReviewService {
    private getAuthHeaders() {
        return {
            'Content-Type': 'application/json',
        };
    }

    async getReviews(page: number = 1, limit: number = 10, search?: string, status?: string, type?: string, rating?: string): Promise<ReviewsResponse> {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString(),
            });

            if (search) params.append('search', search);
            if (status && status !== 'All Statuses') {
                // Convert status to lowercase for API compatibility
                params.append('status', status.toLowerCase());
            }
            if (type && type !== 'All Types') {
                // Convert type to lowercase for API compatibility
                params.append('type', type.toLowerCase());
            }
            if (rating && rating !== 'All Ratings') {
                const ratingNumber = rating.replace(' Star', '').replace('s', '');
                params.append('rating', ratingNumber);
            }

            const response = await fetch(`${REVIEWS_API}?${params.toString()}`, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ReviewsResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching reviews:', error);
            throw error;
        }
    }

    async getStats(): Promise<ReviewStatsResponse> {
        try {
            console.log('Making request to:', REVIEWS_STATS_API);
            const response = await fetch(REVIEWS_STATS_API, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ReviewStatsResponse = await response.json();
            console.log('Raw API response data:', data);
            return data;
        } catch (error) {
            console.error('Error fetching review stats:', error);
            throw error;
        }
    }

    async approveReview(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('statusName', 'Publish');

            const response = await fetch(REVIEW_UPDATE_STATUS_API(id), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error approving review:', error);
            throw error;
        }
    }

    async rejectReview(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('statusName', 'Rejected');

            const response = await fetch(REVIEW_UPDATE_STATUS_API(id), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error rejecting review:', error);
            throw error;
        }
    }

    async archiveReview(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('statusName', 'Archived');

            const response = await fetch(REVIEW_UPDATE_STATUS_API(id), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error archiving review:', error);
            throw error;
        }
    }

    async deleteReview(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(REVIEW_DELETE_API(id), {
                method: 'DELETE',
                headers: this.getAuthHeaders(),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error deleting review:', error);
            throw error;
        }
    }

    async hideReview(id: number, reason: string): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('statusName', 'Hidden');
            formData.append('reason', reason);

            const response = await fetch(REVIEW_HIDDEN_API(id), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error hiding review:', error);
            throw error;
        }
    }

    // ✅ FIXED: Now using dedicated restore endpoint instead of status update
    async restoreReview(id: number): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            // Optional: You can specify a status, otherwise it will restore to previous status
            // formData.append('newStatus', '1'); // Uncomment if you want to force a specific status

            const response = await fetch(REVIEW_RESTORE_API(id), {
                method: 'PATCH',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error restoring review:', error);
            throw error;
        }
    }

    async addNote(id: number, note: string): Promise<ApiResponse> {
        try {
            const formData = new FormData();
            formData.append('note', note);

            const response = await fetch(REVIEW_ADD_NOTE_API(id), {
                method: 'POST',
                credentials: 'include',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error adding note to review:', error);
            throw error;
        }
    }

    async flagReview(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(REVIEW_FLAG_API(id), {
                method: 'PATCH',
                headers: this.getAuthHeaders(),
                credentials: 'include',
                body: JSON.stringify({ flagged: true }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error flagging review:', error);
            throw error;
        }
    }

    async unflagReview(id: number): Promise<ApiResponse> {
        try {
            const response = await fetch(REVIEW_FLAG_API(id), {
                method: 'PATCH',
                headers: this.getAuthHeaders(),
                credentials: 'include',
                body: JSON.stringify({ flagged: false }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ApiResponse = await response.json();
            return data;
        } catch (error) {
            console.error('Error unflagging review:', error);
            throw error;
        }
    }
}

export default new ReviewService();
export type { Review, ReviewsResponse, ApiResponse, ReviewStats, ReviewStatsResponse };
