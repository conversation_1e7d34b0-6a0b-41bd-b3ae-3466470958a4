import IconEdit from '@/components/icon/icon-edit';
import IconPlus from '@/components/icon/icon-plus';
import React, { useEffect, useState } from 'react';
import Modal from '@/components/reusable/modals/modal';

interface AddLeadPopupProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: any, isEdit: boolean) => void;
    initialValues?: any;
    isEdit?: boolean;
    isSubmitting?: boolean;
}

const AddLeadPopup: React.FC<AddLeadPopupProps> = ({ isOpen, onClose, onSubmit, initialValues, isEdit = false, isSubmitting }) => {
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [formValues, setFormValues] = useState({
        fullName: '',
        email: '',
        phone: '',
        licenseNumber: '',
        company: '',
        leadType: '',
        source: '',
    });

    console.log(initialValues);
    useEffect(() => {
        if (initialValues) {
            setFormValues({
                fullName: initialValues.fullName || '',
                email: initialValues.email || '',
                phone: initialValues.phone || '',
                licenseNumber: initialValues.licenseNumber || '',
                company: initialValues.company || '',
                leadType: initialValues.leadType || '',
                source: initialValues.source || '',
            });
        } else {
            setFormValues({
                fullName: '',
                email: '',
                phone: '',
                licenseNumber: '',
                company: '',
                leadType: '',
                source: '',
            });
        }

        setErrors({});
    }, [initialValues]);

    const handleClose = () => {
        setFormValues({
            fullName: '',
            email: '',
            phone: '',
            licenseNumber: '',
            company: '',
            leadType: '',
            source: '',
        });
        setErrors({});
        onClose();
    };

    if (!isOpen) return null;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        setFormValues({ ...formValues, [e.target.name]: e.target.value });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const newErrors: { [key: string]: string } = {};

        // if (!formValues.fullName.trim()) newErrors.fullName = 'Full name is required.';
        if (!formValues.email.trim()) newErrors.email = 'Email address is required.';
        // if (!formValues.phone.trim()) newErrors.phone = 'Phone number is required.';
        // if (!formValues.licenseNumber.trim()) newErrors.licenseNumber = 'License number is required.';
        if (!formValues.leadType.trim()) newErrors.leadType = 'Lead type is required.';
        // if (!formValues.source.trim()) newErrors.source = 'Source is required.';

        setErrors(newErrors);

        if (Object.keys(newErrors).length === 0) {
            onSubmit(formValues, isEdit);
            setFormValues({
                fullName: '',
                email: '',
                phone: '',
                licenseNumber: '',
                company: '',
                leadType: '',
                source: '',
            });
            setErrors({});
        }
    };

    const renderError = (field: string) => errors[field] && <p className="mt-1 text-sm text-red-500">{errors[field]}</p>;

    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="!max-w-1xl">
            <div className=" ">
                <button onClick={handleClose} className="absolute right-4 top-4 text-gray-400 hover:text-gray-600">
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <h2 className="mb-6 flex items-center text-xl font-semibold text-gray-900">
                    <span className="mr-2">{isEdit ? <IconEdit /> : <IconPlus />}</span>
                    {isEdit ? 'Edit Lead' : 'Add New Lead'}
                </h2>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label htmlFor="fullName" className="mb-1 block text-sm font-medium text-gray-700">
                            Full Name
                        </label>
                        <input
                            type="text"
                            id="fullName"
                            name="fullName"
                            value={formValues.fullName}
                            onChange={handleChange}
                            placeholder="Enter full name"
                            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        />
                        {renderError('fullName')}
                    </div>

                    <div>
                        <label htmlFor="email" className="mb-1 block text-sm font-medium text-gray-700">
                            Email Address
                        </label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            value={formValues.email}
                            onChange={handleChange}
                            placeholder="Enter email address"
                            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        />
                        {renderError('email')}
                    </div>

                    <div>
                        <label htmlFor="phone" className="mb-1 block text-sm font-medium text-gray-700">
                            Phone Number
                        </label>
                        <input
                            type="tel"
                            id="phone"
                            name="phone"
                            value={formValues.phone}
                            onChange={handleChange}
                            placeholder="Enter phone number"
                            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        />
                        {renderError('phone')}
                    </div>

                    <div>
                        <label htmlFor="licenseNumber" className="mb-1 block text-sm font-medium text-gray-700">
                            License Number
                        </label>
                        <input
                            type="text"
                            id="licenseNumber"
                            name="licenseNumber"
                            value={formValues.licenseNumber}
                            onChange={handleChange}
                            placeholder="Enter license number"
                            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        />
                        {renderError('licenseNumber')}
                    </div>

                    <div>
                        <label htmlFor="company" className="mb-1 block text-sm font-medium text-gray-700">
                            Company (Optional)
                        </label>
                        <input
                            type="text"
                            id="company"
                            name="company"
                            value={formValues.company}
                            onChange={handleChange}
                            placeholder="Enter company name"
                            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        />
                    </div>

                    <div>
                        <label htmlFor="leadType" className="mb-1 block text-sm font-medium text-gray-700">
                            Lead Type
                        </label>
                        <select
                            id="leadType"
                            name="leadType"
                            value={formValues.leadType}
                            onChange={handleChange}
                            className="w-full appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        >
                            <option value="">Select lead type</option>
                            <option value="agent">Agent</option>
                            <option value="agency">Agency</option>
                        </select>
                        {renderError('leadType')}
                    </div>

                    <div>
                        <label htmlFor="source" className="mb-1 block text-sm font-medium text-gray-700">
                            Source
                        </label>
                        <select
                            id="source"
                            name="source"
                            value={formValues.source}
                            onChange={handleChange}
                            className="w-full appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 focus:border-[#1D7EB6] focus:outline-none focus:ring-1 focus:ring-[#1D7EB6]"
                        >
                            <option value="">Select lead source</option>
                            <option value="Website">Website</option>
                            <option value="Referral">Referral</option>
                            <option value="Cold Outreach">Cold Outreach</option>
                            <option value="Social Media">Social Media</option>
                        </select>
                        {renderError('source')}
                    </div>

                    <div className="mt-6 flex justify-end gap-3">
                        <button type="button" onClick={handleClose} className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        {isSubmitting ? (
                            <>
                                <button
                                    className="flex items-center gap-2 rounded bg-[#1D7EB6] px-4 py-2 text-white hover:bg-[#1D7EB6]/90 disabled:cursor-not-allowed disabled:opacity-50"
                                    disabled={isSubmitting}
                                >
                                    <svg className="h-5 w-5 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                        ></path>
                                    </svg>
                                    Adding User...
                                </button>
                            </>
                        ) : (
                            <>
                                <button type="submit" className="flex items-center rounded-md bg-[#1D7EB6] px-4 py-2 text-sm font-medium text-white hover:bg-[#1D7EB6]/90">
                                    <span className="mr-1">+</span> {isEdit ? 'Update Lead' : 'Add Lead'}{' '}
                                </button>
                            </>
                        )}
                    </div>
                </form>
            </div>
        </Modal>
    );
};

export default AddLeadPopup;
