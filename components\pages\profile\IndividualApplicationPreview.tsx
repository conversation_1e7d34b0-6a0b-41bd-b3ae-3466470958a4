import { AgentProfile } from '@/store/utils.ts/types/AgentProfile';
import images from '@/public/assets/images/main/agent-image.png';
import React, { useState } from 'react';
import DocumentDetail from '../dashboard/components/DocumentDetail';
const IndividualApplicationPreviewComponent = ({ getProfileData }: any) => {
    const [profileData, setProfileData] = useState<AgentProfile>(getProfileData);

    const industryOptions = profileData?.industry_mission?.map((industry) => industry.name).join(', ');

    const typeOptions = profileData?.industry_subcategory?.map((type) => type.name).join(', ');

    const capitalizeFirstLetter = (str: string) => {
        if (!str) return str;

        return str?.charAt(0)?.toUpperCase() + str?.slice(1);
    };

    const renderField = (label: string, value: any) => {
        if (value === null || value === '') return null;
        return (
            <div className="mb-2">
                <span className="pe-1 font-semibold">{label}:</span> {String(value)}
            </div>
        );
    };
    const formatDate = (date: string) => {
        if (!date) return date;
        const dateObj = new Date(date);
        return dateObj.toLocaleDateString('en-US', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
        });
    };
    const designationOption = [
        { value: 'owner', label: 'Owner' },
        { value: 'manager', label: 'Manager' },
        { value: 'authorizedSignatory', label: 'Authorized Signatory' },
        { value: 'adminManager', label: 'Admin Manager' },
        { value: 'other', label: 'Other' },
    ];
    console.log(profileData);
    return (
        <div className="mx-auto max-w-5xl rounded-lg bg-white p-4 font-golosText shadow-lg">
            <h2 className="mb-8 text-center text-3xl font-bold ">Agent Application Information Preview</h2>

            {/* Form 1 - Personal Details */}
            <section className="mb-10">
                <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Personal Information</h5>
                {renderField('First Name', profileData?.firstName)}
                {renderField('Middle Name', profileData?.middleName)}
                {renderField('Last Name', profileData?.lastName)}
                {renderField('Gender', profileData?.gender)}
                {renderField('Phone Number', profileData?.phone)}
                {renderField('Nationality', profileData?.nationality)}
                {profileData?.profileImage && <DocumentDetail type={'Profile Image'} dataArray={[profileData.profileImage]} />}

                {profileData?.operationArea && profileData?.operationArea?.length > 0 && (
                    <div className="flex border-t border-[#e4e4e4] bg-white">
                        <div className="flex w-1/3 items-center bg-neutral-100 py-2 pl-2 pr-2 md:py-3 md:pl-4">
                            <span className="w-full break-words text-sm font-normal text-[#2d2d2e] md:text-base">Operation Areas</span>
                        </div>
                        <div className="flex w-2/3 items-start p-4 py-2 text-sm font-normal text-[#636363] md:text-base">
                            <div className="w-full">
                                {
                                    <ul className="list-disc pl-4">
                                        {profileData?.operationArea?.map((item: any, index: number) => {
                                            return (
                                                <li key={index}>
                                                    {item?.name} - {item?.nationality}{' '}
                                                </li>
                                            );
                                        })}
                                    </ul>
                                }
                            </div>
                        </div>
                    </div>
                )}
            </section>

            {profileData?.companyEmail && (
                <section className="mb-10">
                    <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Company Representative/Manager</h5>
                    {renderField('Designation / Role', profileData?.positionOther ? profileData?.positionOther : designationOption.find((item: any) => item.value == profileData?.position)?.label)}
                    {renderField('Company Email', profileData?.companyEmail)}
                    {renderField('Company Phone', profileData?.companyPhone)}
                </section>
            )}

            {/* Form 4 - Industry Details */}
            <section className="mb-10">
                <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Industry & Specialization</h5>
                {renderField('Primary Industry', industryOptions)}
                {renderField('Specialization', profileData?.industryMissionOther)}

                {profileData?.companyrole?.length > 0 && (
                    <div className="flex border-t border-[#e4e4e4] bg-white">
                        <div className="flex w-1/3 items-center bg-neutral-100 py-2 pl-2 pr-2 md:py-3 md:pl-4">
                            <span className="w-full break-words text-sm font-normal text-[#2d2d2e] md:text-base">Trade License Activities</span>
                        </div>
                        <div className="flex w-2/3 items-start p-4 py-2 text-sm font-normal text-[#636363] md:text-base">
                            <div className="w-full">
                                <ul className="list-disc pl-4">
                                    {profileData?.companyrole?.map((item: any, index: number) => (
                                        <li key={index} className="py-2">
                                            {item?.roleName}
                                            {item?.rolesList?.length > 0 && (
                                                <ul className="list-disc pl-8">
                                                    {item?.rolesList?.map((subRole: any, subIndex: number) => (
                                                        <li key={subIndex}>{subRole}</li>
                                                    ))}
                                                </ul>
                                            )}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    </div>
                )}
            </section>

            {profileData?.agentlicenses?.length > 0 && (
                <section className="mb-10">
                    <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Licensing & Credentials Per Role</h5>

                    {profileData?.agentlicenses?.map((item: any, index: number) => (
                        <div key={index}>
                            {item?.hasLicense ? (
                                <div className="mb-6">
                                    <h6 className="mb-2 text-lg font-semibold">Role ({item.roletype})</h6>

                                    {renderField('Has license or permit or broker card', capitalizeFirstLetter(item?.hasLicense ? 'Yes' : 'No'))}
                                    {renderField('License Number', item?.licenseNumber)}
                                    {renderField('License Authority', item?.licenseAuthority)}
                                    {renderField('License Authority Other', item?.licenseAuthorityOther)}
                                    {renderField('License Expiry Date', formatDate(item?.licenseexpiryDate))}
                                    <DocumentDetail type={'Licence Doc'} dataArray={item?.licenseFile} />
                                </div>
                            ) : (
                                <div className="mb-6">
                                    <h6 className="mb-2 text-lg font-semibold">Role ({item.roletype})</h6>
                                    {renderField('Has license or permit or broker card', capitalizeFirstLetter(item?.hasLicense ? 'Yes' : 'No'))}
                                </div>
                            )}
                        </div>
                    ))}
                </section>
            )}

            {/* Form 7 - Final Documents */}
            <section className="mb-10">
                <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Uploaded Documents</h5>
                {profileData?.emiratesId && profileData?.emiratesId?.length > 0 ? (
                    <DocumentDetail type={'National ID Card'} dataArray={profileData?.emiratesId} />
                ) : (
                    renderField('National ID Card', ' Document is missing')
                )}

                {profileData?.passport && profileData?.passport?.length > 0 ? <DocumentDetail type={'Passport'} dataArray={profileData?.passport} /> : renderField('Passport', ' Document is missing')}

                {profileData?.visa && profileData?.visa?.length > 0 ? <DocumentDetail type={'Visa'} dataArray={profileData?.visa} /> : renderField('Visa', ' Document is missing')}

                {profileData?.passportDoc && profileData?.passportDoc?.length > 0 && <DocumentDetail type={'Passport'} dataArray={profileData?.passportDoc} />}

                {profileData?.personalIdDoc && profileData?.personalIdDoc?.length > 0 && <DocumentDetail type={'Personal Id Doc'} dataArray={profileData?.personalIdDoc} />}

                {profileData?.supportingDocsDoc && profileData?.supportingDocsDoc?.length > 0 && <DocumentDetail type={'Supporting Doc'} dataArray={profileData?.supportingDocsDoc} />}

                {profileData?.visaDoc && profileData?.visaDoc?.length > 0 && <DocumentDetail type={'Visa Doc'} dataArray={profileData?.visaDoc} />}
                {profileData?.emiratesId && profileData?.emiratesId?.length > 0 && <DocumentDetail type={'Emirates Id Doc'} dataArray={profileData?.emiratesId} />}
            </section>
            <section className="mb-10">
                <h5 className="mb-4 border-b pb-2 text-xl font-semibold text-[#1d7eb6]">Confirmation & Consent</h5>
                {renderField('Accuracy Confirmation', profileData?.accuracyConfirm ? 'Yes' : 'No')}
                {renderField('Communication Consent', profileData?.communicationConsent ? 'Yes' : 'No')}
                {renderField('Terms Agreement', profileData?.termsAgree ? 'Yes' : 'No')}
            </section>
        </div>
    );
};

export default IndividualApplicationPreviewComponent;
