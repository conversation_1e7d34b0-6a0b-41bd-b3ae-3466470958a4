'use client';

import type React from 'react';
import { useState } from 'react';

interface AddNoteModalProps {
    service: any;
    onClose: () => void;
    onConfirm: (note: string) => void;
}

export default function AddNoteModal({ service, onClose, onConfirm }: AddNoteModalProps) {
    const [note, setNote] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    if (!service) return null;

    const handleSubmit = async () => {
        if (!note.trim()) {
            return;
        }

        setIsSubmitting(true);
        try {
            await onConfirm(note.trim());
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="w-full max-w-md mx-auto bg-white rounded-lg ">
            {/* Header */}
            <div className="flex justify-between items-start mb-4">
                <h2 className="text-lg font-semibold text-[#2d2d2e] font-inter">Add Note</h2>
                <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600 text-xl w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100"
                >
                    ×
                </button>
            </div>

            {/* Info Icon */}
            <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                </div>
            </div>

            {/* Content */}
            <div className="mb-6">
                <p className="text-sm text-[#636363] font-inter leading-relaxed mb-4">
                    Add a note to service titled <span className="font-medium text-[#2d2d2e]">
                        {service.title}
                    </span>
                </p>

                <div className="mb-4">
                    <label htmlFor="note" className="block text-sm font-medium text-gray-700 mb-2">
                        Note *
                    </label>
                    <textarea
                        id="note"
                        value={note}
                        onChange={(e) => setNote(e.target.value)}
                        placeholder="Enter your note here..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        rows={4}
                        required
                    />
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
                <button
                    onClick={onClose}
                    disabled={isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-[#636363] bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors font-inter disabled:opacity-50"
                >
                    Cancel
                </button>
                <button
                    onClick={handleSubmit}
                    disabled={!note.trim() || isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-white bg-[#2d2d2e] border border-[#2d2d2e] rounded hover:bg-black transition-colors font-inter disabled:opacity-50"
                >
                    {isSubmitting ? 'Adding...' : 'Add Note'}
                </button>
            </div>
        </div>
    );
}
