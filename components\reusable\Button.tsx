export const ReusebleButton = ({ onClick, text, className = "" }:any) => {
    return (
      <button
        onClick={onClick}
        className={`h-14 px-8 w-full py-4 bg-[#1d7eb6] hover:bg-[#37a2db] text-base rounded-lg justify-center items-center gap-2.5 inline-flex text-white font-normal   leading-normal ${className}`}
      >
         {text}
      </button>
    );
  };
  
  export default ReusebleButton;
  