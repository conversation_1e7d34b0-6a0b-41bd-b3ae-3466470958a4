'use client';

import React, { useState } from 'react';
import { SidebarMenu } from '../reusable/SidebarMenu';
import { LogoutIcon } from '../icon/Icon';
import { useRouter } from 'next/navigation';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { showMessage } from '@/app/lib/Alert';
import { clearAllCookies } from '@/app/lib/cookies';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '@/store';
import { setUserSession } from '@/store/slice/user';
import { LogOut, Menu, X } from 'lucide-react';
const Sidebar = () => {
    const dispatch = useDispatch<AppDispatch>();
    const [isSidebarOpen, setIsSidebarOpen] = useState(false)
    const { push } = useRouter();

    const handleLogout = () => {
        try {
            const myHeaders = new Headers();
            const requestOptions: RequestInit = {
                method: 'GET',
                headers: myHeaders,
                redirect: 'follow',
                credentials: 'include',
            };

            fetch(API_ENDPOINTS.LOGOUT, requestOptions)
                .then((response) => response.json())
                .then((result) => {
                    if (result.status === 200) {
                        showMessage('Logout Successfully', 'success');
                        clearAllCookies();
                        push('/login');
                    } else {
                        showMessage('Logout Successfully', 'success');
                        clearAllCookies();
                        push('/login');
                    }
                    dispatch(setUserSession(false));
                })
                .catch((error) => {
                    showMessage('Logout Successfully', 'success');
                    console.error(error);
                    clearAllCookies();
                    push('/login');
                });
        } catch (error) {
            console.error(error);
        }
    };
    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen)
      }
    
      const closeSidebar = () => {
        setIsSidebarOpen(false)
      }
    
    return (
        <div>
           
           <button
        onClick={toggleSidebar}
        className="fixed md:top-5 top-3 left-52 z-[60] xl:hidden bg-[#8B3A3A] p-2 rounded-full text-white shadow-lg hover:bg-[#7A3333] transition-colors"
        aria-label="Toggle sidebar"
      >
        {isSidebarOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0  bg-black bg-opacity-50 z-40 xl:hidden"
          onClick={closeSidebar}
          aria-label="Close sidebar"
        />
      )}

      {/* Sidebar */}
      <nav
        className={`fixed bottom-0 top-0 z-50 h-full min-h-screen w-[260px] shadow-[0_0_5px_0_rgba(94,92,154,0.1)] transition-all duration-300 xl:translate-x-0 ${
          isSidebarOpen ? "translate-x-0" : "-translate-x-full"
        } xl:block`}
        aria-label="Main navigation"
      > 

        <div className="md:mt-[90px] mt-14 flex h-full flex-col overflow-y-auto bg-white  ">
          <SidebarMenu setShowSidebar={closeSidebar} />
          <div className="mb-14 mt-auto md:mb-[90px]">
            <button
              className="flex w-full cursor-pointer items-center gap-4 border-y p-4 text-start text-lg text-[#8B3A3A] hover:bg-[#8B3A3A] hover:text-white transition-colors"
              onClick={handleLogout}
            >
              <span className="flex h-8 w-8 items-center justify-center rounded-full bg-[#8B3A3A] p-2 text-white">
                <LogOut size={16} color="#ffffff" />
              </span>
              <span>Log out</span>
            </button>
            <div className="bg-[#8B3A3A] p-4 text-center text-sm text-white">All Rights Reserved © 2025</div>
          </div>
        </div>
      </nav>
        </div>
    );
};

export default Sidebar;
