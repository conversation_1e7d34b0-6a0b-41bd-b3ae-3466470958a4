// userSlice.ts
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    userSession: false,
    userDetails: {},
};

const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        setUserSession(state, action) {
            state.userSession = action.payload;
        },
        setUserDetails(state, action) {
            state.userDetails = action.payload;
        },
    },
});

export const { setUserSession, setUserDetails } = userSlice.actions;
export default userSlice.reducer;
