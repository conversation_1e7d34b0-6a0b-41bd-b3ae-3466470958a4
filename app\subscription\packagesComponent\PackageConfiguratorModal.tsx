import React, { useState, useEffect } from 'react';
import InputField from '@/components/reusable/InputField';
import TextArea from '@/components/reusable/TextArea';
import { Dropdown } from '@/components/reusable/Dropdown';
import ReusebleButton from '@/components/reusable/Button';
import { ColorThemeOptions } from '@/components/subscription/ColorThemeEnum';
import axios from 'axios';
import { PACKAGES_API, FEATURE_VALUES_BY_PACKAGE_USER_TYPE_API, FEATURE_VALUES_API, STATUS_API, FEATURES_API, DISCOUNTS_API } from '@/app/lib/apiRoutes';
import { AutoLeadsAssigningPriorityEnum, VisibilityLevelEnum, FrontLandingPageListingEnum, RiskOfBeingBlacklistedEnum } from '@/app/constants/subscriptionEnums';
import { Feature, FeatureValue } from '@/app/types/subscription';
import { Discount } from '@/components/pages/discout-management/DiscountManagement';
import { showMessage } from '@/app/lib/Alert';

// Props for the modal
interface PackageConfiguratorModalProps {
    isOpen: boolean;
    onClose: () => void;
    packageData: any; // You can replace 'any' with a more specific type if available
    onSave: () => void;
    statuses?: { id: number; name: string }[];
}

type ApiResponse<T> = { data: T; [key: string]: any };

const modalBackdrop = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30';
const modalContent = 'bg-white rounded-xl shadow-lg w-full max-w-3xl p-0 flex flex-col';

export default function PackageConfiguratorModal({ isOpen, onClose, packageData, onSave, statuses = [] }: PackageConfiguratorModalProps) {
  const [form, setForm] = useState<any>({ ...packageData });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [featureValues, setFeatureValues] = useState<any[]>([]);
    const [featureDefinitions, setFeatureDefinitions] = useState<Feature[]>([]);
    const [discountOptions, setDiscountOptions] = useState<{ value: number; label: string }[]>([]);

    useEffect(() => {
        // Set form state and active status from statuses
        const statusObj = statuses.find((s) => s.id === packageData.statusId);
        const isActive = statusObj?.name === 'Activated';
        setForm((f: any) => ({ ...packageData, active: isActive }));
    }, [packageData, statuses]);

    // Fetch feature values for the package on mount
    useEffect(() => {
        const fetchDiscounts = async () => {
            try {
                const res = await axios.get(DISCOUNTS_API, { withCredentials: true });

                const activeOnly = (res.data as any).data;

                const options = activeOnly.map((d: Discount) => {
                    const priceDisplay = d.type === 'fixed' ? `${d.value} AED` : `${d.value}%`;
                    return {
                        value: d.id,
                        label: `${d.name} (${priceDisplay})`,
                    };
                });

                setDiscountOptions(options);
            } catch (err) {
                console.error('Failed to load discounts', err);
                setDiscountOptions([]);
            }
        };

        async function fetchFeatureValues() {
            if (!packageData?.id || !packageData?.userType) return;
            setLoading(true);
            setError(null);
            try {
                const res = await axios.get<ApiResponse<FeatureValue[]>>(`${FEATURE_VALUES_BY_PACKAGE_USER_TYPE_API}/${packageData.id}/user-type/${packageData.userType}`, {
                    withCredentials: true,
                });
                setFeatureValues(res.data && res.data.data ? res.data.data : []);
                // Map feature values to form (no coercion for enums)
                const featuresObj: any = {};
                (res.data && res.data.data ? res.data.data : []).forEach((fv: any) => {
                    featuresObj[fv.featureName] = fv.featureType === 'BOOLEAN' ? fv.featureValue === 'true' || fv.featureValue === true : fv.featureValue;
                });
                setForm((f: any) => ({ ...f, features: featuresObj }));
            } catch (err: any) {
                setError(err.response?.data?.message || 'Failed to fetch feature values');
                setFeatureValues([]);
            } finally {
                setLoading(false);
            }
        }

        fetchDiscounts();
        fetchFeatureValues();
    }, [packageData]);

    // Fetch feature definitions from backend
    useEffect(() => {
        async function fetchFeatureDefinitions() {
            try {
                const res = await axios.get<ApiResponse<Feature[]>>(FEATURES_API, {
                    withCredentials: true,
                });
                setFeatureDefinitions(res.data.data ? res.data.data : []);
            } catch (err) {
                setFeatureDefinitions([]);
            }
        }
        fetchFeatureDefinitions();
    }, []);

    const handleBasicChange = (field: string, value: any) => setForm((f: any) => ({ ...f, [field]: value }));
    const handleFeatureChange = (featureName: string, value: any) => {
        setForm((f: any) => ({
            ...f,
            features: {
                ...f.features,
                [featureName]: value,
            },
        }));
    };

    // Color theme dropdown data
    const colorThemeDropdownData = ColorThemeOptions.map((opt) => ({
        value: opt.value,
        label: (
            <div className="flex items-center">
                <div className={`mr-2 h-4 w-4 rounded ${opt.className}`}></div>
                {opt.label}
            </div>
        ),
    }));

    // Custom styles for react-select to avoid black hover
    const dropdownCustomStyles = {
        control: (styles: any) => ({
            ...styles,
            maxWidth: '100%',
            width: '100%',
            backgroundColor: 'none',
            color: '#292929',
            borderRadius: '8px',
            borderColor: 'lightGray',
            boxShadow: 'none',
            borderWidth: '1px',
            outline: 'none',
            fontSize: '14px',
            fontWeight: 'normal',
            padding: '',
            paddingTop: '8px',
            paddingBottom: '8px',
            cursor: 'pointer',
        }),
        option: (styles: any, { isSelected, isFocused }: any) => ({
            ...styles,
            backgroundColor: isSelected ? '#cacace' : isFocused ? '#f1f6f5' : '#EEEDF4',
            color: '#292929',
            borderBottom: '1px solid #cacace',
            paddingLeft: '22px',
            paddingRight: '22px',
            paddingTop: '14px',
            paddingBottom: '14px',
            ':last-child': {
                marginBottom: '10px',
            },
        }),
        menu: (styles: any) => ({
            ...styles,
            backgroundColor: '#F1F6F5',
            borderRadius: '25px',
            border: 'none',
            padding: '0px',
            boxShadow: 'none',
            color: '#292929',
            margin: '0px',
            left: '1px',
            zIndex: 9999,
            width: '100%',
        }),
        menuList: (styles: any) => ({
            ...styles,
            borderRadius: '4px',
            color: '#292929',
            paddingTop: '0px',
            backgroundColor: '#EEEDF4',
            minWidth: '230px',
            outline: 'none',
            fontSize: '14px',
            fontWeight: 'normal',
        }),
        indicatorSeparator: () => ({ display: 'none' }),
        singleValue: (styles: any) => ({
            ...styles,
            color: '#292929',
            border: 'none',
            fontSize: '14px',
            paddingTop: '10px',
        }),
        input: (styles: any) => ({ ...styles, color: '#292929', fontSize: '14px' }),
        placeholder: (styles: any) => ({ ...styles, color: '#989898', fontSize: '14px' }),
        menuPortal: (base: any) => ({ ...base, zIndex: 99999 }),
    };

    // Find selected color theme option
    const selectedColorTheme = ColorThemeOptions.find((opt) => opt.value === form.colorTheme);
    const selectedColorThemeDropdownValue = selectedColorTheme
        ? {
              label: (
                  <div className="flex items-center">
                      <div className={`mr-2 h-4 w-4 rounded ${selectedColorTheme.className}`}></div>
                      {selectedColorTheme.label}
                  </div>
              ),
              value: selectedColorTheme.value,
          }
        : undefined;

    // Helper to get enum options by feature constant
    const getEnumOptions = (featureConstant: string) => {
        switch (featureConstant) {
            case 'AUTO_LEADS_PRIORITY':
                return Object.values(AutoLeadsAssigningPriorityEnum);
            case 'VISIBILITY_LEVEL':
                return Object.values(VisibilityLevelEnum);
            case 'FRONT_PAGE_LISTING':
                return Object.values(FrontLandingPageListingEnum);
            case 'BLACKLIST_RISK':
                return Object.values(RiskOfBeingBlacklistedEnum);
            default:
                return [];
        }
    };

    // Save handler: update package and feature values
    const handleSave = async () => {
        setLoading(true);
        setError(null);
        try {
            // 1. Update package info
            const statusRes = await axios.get(STATUS_API);
            const statusList = (statusRes.data as any).data;
            const statusObj = statusList.find((s: any) => s.name === (form.active ? 'Activated' : 'Deactivated'));
            const statusId = statusObj ? statusObj.id : 1;
            const pkgReqBody = {
                name: form.name,
                description: form.description,
                statusId,
                price: Number(form.price),
                currency: 'AED',
                userType: packageData.userType,
                colorTheme: form.colorTheme,
                discountId: form.discountId || null,
            };

            const price = Number(pkgReqBody.price);

            // ✅ Validate price
            if (isNaN(price) || price <= 0) {
                showMessage('Package price must be greater than zero.', 'error');
                setLoading(false);
                return;
            }

            // ✅ If a discount is selected, validate that it won't zero or negatively affect the price
            if (pkgReqBody.discountId) {
                const discount = discountOptions.find((d) => d.value === pkgReqBody.discountId);
                if (discount) {
                    const isPercentage = discount.label.includes('%');
                    const discountValue = parseFloat(discount.label.match(/[\d.]+/)?.[0] || '0');

                    let finalPrice = isPercentage ? price - (price * discountValue) / 100 : price - discountValue;

                    if (finalPrice <= 0) {
                        showMessage(`Final price after applying discount is ${finalPrice.toFixed(2)} AED, which is not allowed. It must be greater than zero.`, 'error');
                        setLoading(false);
                        return;
                    }
                }
            }

            await axios.put(`${PACKAGES_API}/${packageData.id}`, pkgReqBody, {
                withCredentials: true,
            });
            // 2. Update or insert feature values for all features in featureDefinitions
            for (const feature of featureDefinitions) {
                const value = form.features?.[feature.featureName];
                // Find the matching feature value entry (if exists)
                const existingFeatureValue = featureValues.find((fv) => fv.featureId === feature.featureId);
                if (existingFeatureValue) {
                    await axios.put(
                        `${FEATURE_VALUES_API}/${existingFeatureValue.packageFeatureId}`,
                        {
                            packageTypeId: packageData.id,
                            featureId: feature.featureId,
                            featureValue: feature.featureType === 'BOOLEAN' ? !!value : value,
                        },
                        {
                            withCredentials: true,
                        }
                    );
                } else {
                    // Insert new feature value if not present
                    await axios.post(
                        `${FEATURE_VALUES_API}`,
                        {
                            packageTypeId: packageData.id,
                            featureId: feature.featureId,
                            featureValue: feature.featureType === 'BOOLEAN' ? !!value : value,
                        },
                        {
                            withCredentials: true,
                        }
                    );
                }
            }
            onSave();
        } catch (err: any) {
            setError(err.response?.data?.message || 'Failed to save changes');
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className={modalBackdrop} onClick={onClose}>
            <div className={modalContent} onClick={(e) => e.stopPropagation()}>
                {/* Modal Header */}
                <div className="flex items-center justify-between border-b border-[#e4e4e4] px-8 pb-2 pt-8">
                    <h2 className="text-2xl font-bold text-[#2d2d2e]">Edit Package: {form.name}</h2>
                    <button onClick={onClose} className="text-2xl text-[#993333]">
                        &times;
                    </button>
                </div>
                {/* Modal Body (Scrollable) */}
                <div className="overflow-y-auto px-8 py-10" style={{ maxHeight: '70vh' }}>
                    {/* Basic Details */}
                    <div className="mb-6 grid grid-cols-2 gap-6">
                        <InputField id="name" label="Package Name" value={form.name} onChange={(e) => handleBasicChange('name', e.target.value)} placeholder="Enter package name" />
                        <InputField id="price" label="Price (AED)" type="number" value={form.price} onChange={(e) => handleBasicChange('price', e.target.value)} placeholder="0" />
                    </div>

                    {form.price && Number(form.price) >= 0 && (
                        <div className="mb-4 grid grid-cols-1 gap-4">
                            <Dropdown
                                label="Select Discount (Optional)"
                                data={discountOptions}
                                value={discountOptions.find((opt) => opt.value === form.discountId) || { value: null, label: 'None' }}
                                onHandleClick={(selected: any) => handleBasicChange('discountId', selected.value)}
                                width="w-full z-[9999]"
                            />
                        </div>
                    )}

                    <div className="mb-8">
                        <label className="mb-2 block font-medium text-[#555]" htmlFor="description">
                            Description
                        </label>
                        <TextArea
                            id="description"
                            value={form.description || ''}
                            onChange={(e: { target: { value: any } }) => handleBasicChange('description', e.target.value)}
                            placeholder="Enter package description"
                            rows={3}
                            className="mb-2"
                        />
                    </div>
                    {/* Color Theme and Active Toggle */}
                    <div className="mb-10 grid grid-cols-2 items-end gap-6">
                        <div>
                            <Dropdown
                                label="Color Theme"
                                data={colorThemeDropdownData}
                                value={selectedColorThemeDropdownValue}
                                onHandleClick={(selected: any) => handleBasicChange('colorTheme', selected.value)}
                                width="w-full z-[9999]"
                                styles={dropdownCustomStyles}
                            />
                        </div>
                        <div className="mt-6 flex items-center gap-3">
                            <label className="relative inline-flex cursor-pointer select-none items-center">
                                <input
                                    type="checkbox"
                                    checked={form.active}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleBasicChange('active', e.target.checked)}
                                    className="peer sr-only"
                                />
                                <div className={`relative h-6 w-10 rounded-full transition-colors duration-200 ${form.active ? 'bg-[#2d2d2e]' : 'bg-[#9f9fa7]'}`}>
                                    <div className={`absolute left-0.5 top-0.5 h-5 w-5 rounded-full bg-white shadow-md transition-transform duration-200 ${form.active ? 'translate-x-4' : ''}`}></div>
                                </div>
                                <span className={`ml-3 text-base font-medium`}>{form.active ? 'Active' : 'Inactive'}</span>
                            </label>
                        </div>
                    </div>
                    {/* Feature Configuration */}
                    <h3 className="mb-4 mt-10 text-lg font-semibold text-[#2d2d2e]">Feature Configuration</h3>
                    <div className="flex flex-col gap-6">
                        {featureDefinitions.map((feature) => {
                            const value = form.features?.[feature.featureName] ?? '';
                            const type = feature.featureType.toLowerCase();
                            let options: string[] = [];
                            if (type === 'enum') {
                                options = getEnumOptions(feature.featureConstant);
                            }
                            return (
                                <div key={feature.featureId} className="mb-1">
                                    <label className="mb-2 block font-medium text-[#555]">{feature.featureName}</label>
                                    {type === 'text' && (
                                        <InputField
                                            id={feature.featureName}
                                            value={value}
                                            onChange={(e) => handleFeatureChange(feature.featureName, e.target.value)}
                                            className="border border-[#e4e4e4]"
                                        />
                                    )}
                                    {type === 'numeric' && (
                                        <InputField
                                            id={feature.featureName}
                                            type="number"
                                            value={value}
                                            onChange={(e) => handleFeatureChange(feature.featureName, e.target.value)}
                                            className="border border-[#e4e4e4]"
                                        />
                                    )}
                                    {type === 'enum' && (
                                        <Dropdown
                                            data={options.map((opt) => ({ label: opt, value: opt }))}
                                            value={options.some((opt) => opt === value) ? { label: value, value } : undefined}
                                            onHandleClick={(selected: any) => handleFeatureChange(feature.featureName, selected.value)}
                                            width="w-full z-[9999]"
                                            styles={dropdownCustomStyles}
                                        />
                                    )}
                                    {type === 'boolean' && (
                                        <label className="relative mt-2 inline-flex cursor-pointer select-none items-center">
                                            <input
                                                type="checkbox"
                                                checked={!!value}
                                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleFeatureChange(feature.featureName, e.target.checked)}
                                                className="peer sr-only"
                                            />
                                            <div className={`relative h-6 w-10 rounded-full transition-colors duration-200 ${value ? 'bg-[#2d2d2e]' : 'bg-[#9f9fa7]'}`}>
                                                <div
                                                    className={`absolute left-0.5 top-0.5 h-5 w-5 rounded-full bg-white shadow-md transition-transform duration-200 ${value ? 'translate-x-4' : ''}`}
                                                ></div>
                                            </div>
                                            <span className={`ml-3 text-base font-medium`}>{value ? 'Enabled' : 'Disabled'}</span>
                                        </label>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                </div>
                {/* Modal Footer */}
                <div className="flex justify-end gap-3 border-t border-[#e4e4e4] bg-[#f9f9f9] px-8 py-4">
                    <ReusebleButton onClick={onClose} text="Cancel" className="min-w-[120px] bg-[#993333] hover:bg-[#711d1d]" />
                    <ReusebleButton onClick={handleSave} text="Save Changes" className="min-w-[120px] bg-[#993333] hover:bg-[#711d1d]" />
                </div>
            </div>
        </div>
    );
}
