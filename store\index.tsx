import themeConfigSlice from '@/store/themeConfigSlice';


import { combineReducers, configureStore } from "@reduxjs/toolkit";
import loadingSlice from "@/store/slice/loading";
import userSlice from "@/store/slice/user";

const rootReducer = combineReducers({
  loader: loadingSlice,
  user: userSlice,
  themeConfig: themeConfigSlice,
});

export const store = configureStore({
  reducer: rootReducer,
});

export type IRootState = ReturnType<typeof rootReducer>;
export type AppDispatch = typeof store.dispatch;
