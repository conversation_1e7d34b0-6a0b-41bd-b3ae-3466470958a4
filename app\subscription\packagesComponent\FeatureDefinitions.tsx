import React, { useState, useEffect } from 'react';
import FeatureModal, { FeatureFormData } from './FeatureModal';
import { Plus } from 'lucide-react';
import axios from 'axios';
import { FEATURES_API } from '@/app/lib/apiRoutes';
import { Feature } from '@/app/types/subscription';
import {
  AutoLeadsAssigningPriorityEnum,
  VisibilityLevelEnum,
  FrontLandingPageListingEnum,
  RiskOfBeingBlacklistedEnum
} from '@/app/constants/subscriptionEnums';
// import StatusBadge from '@/components/reusable/StatusBandage';
// import IconSettings from '@/components/icon/icon-settings';
// import { ReusebleButton } from '@/components/reusable/Button';

export const EditIcon = () => (
  <svg width="18" height="18" fill="none" stroke="#2d2d2e" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" viewBox="0 0 24 24"><path d="M12 20h9" /><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4 12.5-12.5z" /></svg>
);
export const TrashIcon = () => (
  <svg width="18" height="18" fill="none" stroke="#993333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" viewBox="0 0 24 24"><polyline points="3 6 5 6 21 6" /><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2" /></svg>
);

const getEnumValues = (featureConstant: string) => {
  switch (featureConstant) {
    case 'AUTO_LEADS_PRIORITY':
      return Object.values(AutoLeadsAssigningPriorityEnum).join(', ');
    case 'VISIBILITY_LEVEL':
      return Object.values(VisibilityLevelEnum).join(', ');
    case 'FRONT_PAGE_LISTING':
      return Object.values(FrontLandingPageListingEnum).join(', ');
    case 'BLACKLIST_RISK':
      return Object.values(RiskOfBeingBlacklistedEnum).join(', ');
    default:
      return '-';
  }
};

const FeatureDefinitions = () => {
  const [features, setFeatures] = useState<Feature[]>([]);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState<FeatureFormData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeatures = async () => {
      try {
        const res = await axios.get(FEATURES_API, {
          withCredentials: true,
        });
        setFeatures(((res.data as any)?.data) ? (res.data as any).data : []);
      } catch (err) {
        setFeatures([]);
      }
    };
    fetchFeatures();
  }, []);

  const handleDeleteFeature = async (featureId: number) => {
    setLoading(true);
    setError(null);
    try {
      await axios.delete(`${FEATURES_API}/${featureId}`, {
        withCredentials: true,
      });
      const res = await axios.get(FEATURES_API, {
        withCredentials: true,
      });
      setFeatures(((res.data as any)?.data) ? (res.data as any).data : []);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete feature');
      setFeatures([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 font-inter">
      {/* <div className="flex justify-end pb-4"> */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-[#2d2d2e]">Features</h2>
        <button onClick={() => setShowAddModal(true)} className="bg-[#993333] text-white px-5 py-2 rounded-lg font-medium hover:bg-[#711d1d] transition-all text-base flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Add Feature
        </button>
      </div>
      {/* Feature Listing Table */}
      <div className="bg-white rounded-lg border border-[#e4e4e4]">
        <div className="px-6 py-4 border-b border-[#e4e4e4]">
          <h3 className="text-lg font-semibold text-[#2d2d2e]">Feature Definitions</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse font-inter">
            <thead className="bg-[#e4e4e4]">
              <tr>
                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Order</th>
                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Name</th>
                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Type</th>
                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Constants</th>
                <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Enum Values</th>
                {/* <th className="h-[56px] px-4 text-left text-base font-medium text-[#2d2d2e]">Status</th> */}
                <th className="h-[56px] px-4 text-right text-base font-medium text-[#2d2d2e]">Actions</th>
              </tr>
            </thead>
            <tbody>
              {features && features.length > 0 && features.map((feature) => (
                <tr key={feature.featureId} className="border-b border-[#e4e4e4] hover:bg-neutral-100">
                  <td className="h-[56px] px-4 align-middle">
                    <div className="flex items-center pl-4">
                      {feature.displayOrder}
                    </div>
                  </td>
                  <td className="h-[56px] px-4 align-middle font-semibold text-[#2d2d2e]">{feature.featureName}</td>
                  <td className="h-[56px] px-4 align-middle text-[#636363]">{feature.featureType}</td>
                  <td className="h-[56px] px-4 align-middle text-[#636363]">{feature.featureConstant}</td>
                  {/* <td className="h-[56px] px-4 align-middle text-[#636363]">{feature.enumValues}</td> */}
                  <td className="h-[56px] px-4 align-middle text-[#636363]">
                    {feature.featureType === 'ENUM'
                      ? getEnumValues(feature.featureConstant)
                      : '-'}
                  </td>
                  {/* <td className="h-[56px] px-4 align-middle">
                    <StatusBadge status={feature.status} />
                  </td> */}
                  <td className="space-y-2  px-4 align-middle text-right">
                    <button onClick={() => {
                      setSelectedFeature({
                        featureId: feature.featureId,
                        name: feature.featureName,
                        type: feature.featureType,
                        constants: feature.featureConstant === '-' ? '' : feature.featureConstant,
                        displayOrder: feature.displayOrder
                      }); 
                      setShowEditModal(true);
                    }} className="inline-flex items-center gap-2 px-3 py-2 rounded border border-[#e4e4e4] bg-white hover:bg-neutral-100 text-[#2d2d2e] font-medium transition-all xl:mr-2 max-md:mb-2">
                      <EditIcon />
                    </button>
                    <button
                      className="inline-flex items-center gap-2 px-3 py-2 rounded border border-[#e4e4e4] bg-white hover:bg-neutral-100 text-[#993333] font-medium transition-all"
                      onClick={() => handleDeleteFeature(feature.featureId)}
                    >
                      <TrashIcon />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      {/* Add/Edit Feature Modal Skeleton */}
      <FeatureModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSave={async (data) => {
          setLoading(true);
          setError(null);
          try {
            const reqBody = {
              featureName: data.name,
              featureType: data.type.toUpperCase(),
              featureConstant: data.constants || '',
              displayOrder: data.displayOrder,
              description: '', // Optionally add description field
            };
            await axios.post(FEATURES_API, reqBody, {
              withCredentials: true,
            });
            const res = await axios.get(FEATURES_API, {
              withCredentials: true,
            });
            setFeatures(((res.data as any)?.data) ? (res.data as any).data : []);
            setShowAddModal(false);
          } catch (err: any) {
            setError(err.response?.data?.message || 'Failed to add feature');
            setFeatures([]);
          } finally {
            setLoading(false);
          }
        }}
        mode="add"
      />
      <FeatureModal
        isOpen={showEditModal}
        onClose={() => { setShowEditModal(false); setSelectedFeature(null); }}
        onSave={async (data) => {
          setLoading(true);
          setError(null);
          try {
            const featureId = data.featureId || (selectedFeature && selectedFeature.featureId);
            if (!featureId) throw new Error('Feature ID not found');
            const reqBody = {
              featureName: data.name,
              featureType: data.type,
              featureConstant: data.constants || '',
              displayOrder: data.displayOrder,
              description: '',
            };
            await axios.put(`${FEATURES_API}/${featureId}`, reqBody, {
              withCredentials: true,
            });
            const res = await axios.get(FEATURES_API, {
              withCredentials: true,
            });
            setFeatures(((res.data as any)?.data) ? (res.data as any).data : []);
            setShowEditModal(false);
            setSelectedFeature(null);
          } catch (err: any) {
            setError(err.response?.data?.message || 'Failed to edit feature');
            setFeatures([]);
          } finally {
            setLoading(false);
          }
        }}
        mode="edit"
        initialValues={selectedFeature || undefined}
      />
    </div>
  );
};

export default FeatureDefinitions; 