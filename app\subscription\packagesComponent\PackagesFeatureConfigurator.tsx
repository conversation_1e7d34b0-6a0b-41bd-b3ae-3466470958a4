import React, { useState } from 'react';
import PackageConfiguration from './PackageConfiguration';
import FeatureDefinitions from './FeatureDefinitions';

const PackagesFeatureConfigurator = () => {
  const [activeTab, setActiveTab] = useState<'package' | 'features'>('package');

  return (
    <div className="w-full font-inter">
      {/* Header */}
      <div className="bg-white rounded-t-lg shadow-sm border-b">
        {/* <div className="md:px-8 px-4 py-8 text-center">
          <h1 className="lg:text-4xl text-2xl font-bold text-[#2d2d2e] mb-2">Package & Feature Configurator</h1>
          <p className="text-[#636363] lg:text-lg text-base">Configure and customize your agent packages and its features with dynamic pricing and feature management</p>
        </div> */}
        {/* Tab Navigation */}
        <div className="flex md:space-x-8 space-x-4 justify-start md:px-8 px-4 border-b">
          <button
            onClick={() => setActiveTab('package')}
            className={`py-4 px-2 border-b-2 font-medium text-base  focus:outline-none transition-all ${activeTab === 'package' ? 'border-[#993333] text-[#993333] font-semibold' : 'border-transparent text-[#636363] hover:text-[#993333]'}`}
          >
            Package Configuration
          </button>
          <button
            onClick={() => setActiveTab('features')}
            className={`py-4 px-2 border-b-2 font-medium text-base focus:outline-none transition-all ${activeTab === 'features' ? 'border-[#993333] text-[#993333] font-semibold' : 'border-transparent text-[#636363] hover:text-[#993333]'}`}
          >
            Feature Management
          </button>
        </div>
      </div>
      {/* Content */}
      <div className="  py-8">
        {activeTab === 'package' ? (
          <PackageConfiguration />
        ) : (
          <FeatureDefinitions />
        )}
      </div>
    </div>
  );
};

export default PackagesFeatureConfigurator; 