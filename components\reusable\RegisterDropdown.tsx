"use client";
import React, { useState, useEffect, useRef } from "react";

interface DropdownOption {
  value: string;
  label: string;
  [key: string]: any; 
}

interface DropdownProps {
  options: DropdownOption[];
  value?: string;
  onChange?: (selectedValue: string, selectedOption: DropdownOption) => void;
  placeholder?: string;
  className?: string;
  dropdownClassName?: string;
  optionClassName?: string;
  disabled?: boolean;
  searchable?: boolean;
  maxHeight?: string;
}

const RegisterDropdown: React.FC<DropdownProps> = ({
  options,
  value = "",
  onChange,
  placeholder = "Select an option",
  className = "",
  dropdownClassName = "",
  optionClassName = "",
  disabled = false,
  searchable = false,
  maxHeight = "160px",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setSearchTerm("");
      }
    }
  };

  const selectOption = (option: DropdownOption) => {
    if (onChange) {
      onChange(option.value, option);
    }
    setIsOpen(false); // Close dropdown after selection
  };

  const filteredOptions = searchable
    ? options.filter((option) =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  const selectedOption = options.find((option) => option.value === value);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div ref={dropdownRef} className={`relative inline-block w-full ${className}`}>
      <div
        onClick={toggleDropdown}
        className={`cursor-pointer flex items-center justify-between gap-2 px-5 py-4 rounded-lg border border-[#e4e4e4] ${
          isOpen ? "border-[#1d7eb6] ring-1 ring-[#1d7eb6]" : "border-gray-300"
        } ${disabled ? "bg-gray-100 cursor-not-allowed opacity-70" : ""}`}
      >
        <p className={`${!value ? "text-gray-400" : "text-gray-800"}`}>
          {selectedOption ? selectedOption.label : placeholder}
        </p>
        <svg
          className={`w-4 h-4 text-gray-600 transform transition-transform ${
            isOpen ? "rotate-180" : "rotate-0"
          }`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>

      {isOpen && (
        <div
          className={`absolute mt-1 rounded-lg border border-[#e4e4e4] z-10 w-full bg-white ${dropdownClassName}`}
        >
          {searchable && (
            <div className="p-2 border-b border-gray-200">
              <input
                type="text"
                placeholder="Search..."
                className="w-full p-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-300"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                autoFocus
              />
            </div>
          )}
          <ul
            className={`overflow-y-scroll h-40  scrollbar-main-22  ${maxHeight ? `max-h-[${maxHeight}]` : ""}`}
          >
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => (
                <li
                  key={option.value}
                  className={`px-3 py-2 cursor-pointer hover:bg-gray-100 ${
                    value === option.value ? "bg-blue-50 text-[#1d7eb6]" : ""
                  } ${optionClassName}`}
                  onClick={() => selectOption(option)}
                >
                  {option.label}
                </li>
              ))
            ) : (
              <li className="px-3 py-2 text-gray-500">No options found</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default RegisterDropdown;
