'use client';
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import BreadCrumButton from '@/components/reusable/BreadCrumButton';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { PaginationDownIcon, PaginationLeftIcon, PaginationRightIcon } from '@/components/icon/Icon';
import { getPaginationRange, useDebouncedValue } from '@/store/utils.ts/functions';
import Loading from '@/components/layouts/loading';
import SearchInput from '@/components/reusable/SearchBar';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import { EditIcon, TrashIcon } from '@/app/subscription/packagesComponent/FeatureDefinitions';
import AddAndUpdateDiscountPopup from './AddAndUpdateDiscountPopup';
import { showMessage } from '@/app/lib/Alert';
import Modal from '@/components/reusable/modals/modal';

const dropdown = [{ label: 'All' }, { label: 'Active' }, { label: 'Inactive' }];

export interface Discount {
    id: number;
    name: string;
    type: string;
    value: string;
    code: string;
    status_name: string;
    created_at: string;
}

const columns = [
    { key: 'name', label: 'Name', width: 'w-48' },
    { key: 'type', label: 'Type', width: 'w-32' },
    { key: 'value', label: 'Value', width: 'w-32' },
    // { key: 'code', label: 'Code', width: 'w-32' },
    { key: 'created_at', label: 'Created At', width: 'w-32' },
    { key: 'status', label: 'Status', width: 'w-32' },
    { key: 'actions', label: 'Actions', width: 'w-32' },
];

const DiscountManagementLayout = () => {
    const { push } = useRouter();
    const [showDropdown, setShowDropdown] = useState(false);
    const [dropUp, setDropUp] = useState(false);
    const [discounts, setDiscounts] = useState<Discount[]>([]);
    const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });
    const [searchTerm, setSearchTerm] = useState('');
    const debouncedSearchTerm = useDebouncedValue(searchTerm, 300);
    const [loader, setLoader] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState('All');
    const [showAddModal, setShowAddModal] = useState(false);
    const [editDiscount, setEditDiscount] = useState<Discount | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [deleteTarget, setDeleteTarget] = useState<Discount | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);

    const fetchDiscounts = useCallback(async () => {
        setLoader(true);

        const params = new URLSearchParams({
            page: pagination.page.toString(),
            pageSize: pagination.pageSize.toString(),
            search: debouncedSearchTerm,
            status: selectedStatus === 'All' ? '' : selectedStatus,
        });

        try {
            const res = await fetch(`${API_ENDPOINTS.DISCOUNTS}?${params}`, {
                credentials: 'include',
            });
            const data = await res.json();

            if (data.success) {
                setDiscounts(data.data.discounts);
                setPagination((prev) => ({
                    ...prev,
                    total: data.data.pagination.total,
                }));
            }
        } catch (error) {
            console.error('Error fetching discounts:', error);
        } finally {
            setLoader(false);
        }
    }, [pagination.page, pagination.pageSize, debouncedSearchTerm, selectedStatus]);

    useEffect(() => {
        fetchDiscounts();
    }, [fetchDiscounts]);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        setPagination((prev) => ({ ...prev, page: 1 }));
    };

    const totalPages = Math.ceil(pagination.total / pagination.pageSize);
    const setPageSafe = (n: number) => setPagination((prev) => ({ ...prev, page: Math.min(Math.max(n, 1), totalPages || 1) }));

    const handleSubmitDiscount = async (formData: any, isEdit: boolean) => {
        setIsSubmitting(true);

        const url = isEdit ? `${API_ENDPOINTS.DISCOUNTS}/${editDiscount?.id}` : API_ENDPOINTS.DISCOUNTS;

        const method = isEdit ? 'PUT' : 'POST';

        const payload = new FormData();
        payload.append('name', formData.name);
        payload.append('description', formData.description || '');
        payload.append('type', formData.type);
        payload.append('value', formData.value);
        payload.append('code', formData.code || '');

        try {
            const res = await fetch(url, {
                method,
                credentials: 'include',
                body: payload,
            });

            const data = await res.json();

            if (data.success) {
                setShowAddModal(false);
                setEditDiscount(null);
                showMessage(data.message || 'Discount saved successfully', 'success');
                fetchDiscounts();
            } else {
                showMessage(data.message || 'Something went wrong', 'error');
            }
        } catch (err) {
            console.error('Discount submission failed:', err);
            showMessage('Server error. Please try again.', 'error');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleDeleteDiscount = async () => {
        if (!deleteTarget) return;

        setIsDeleting(true);

        try {
            const res = await fetch(`${API_ENDPOINTS.DISCOUNTS}/${deleteTarget.id}`, {
                method: 'DELETE',
                credentials: 'include',
            });

            const data = await res.json();

            if (data.success) {
                showMessage(data.message || 'Discount deleted successfully', 'success');
                fetchDiscounts();
            } else {
                showMessage(data.message || 'Failed to delete discount', 'error');
            }
        } catch (error) {
            console.error('Delete discount failed:', error);
            showMessage('Server error during deletion.', 'error');
        } finally {
            setIsDeleting(false);
            setDeleteTarget(null);
        }
    };

    return (
        <DefaultPageLayout>
            <BreadCrums
                mainHeading="Discount Management"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Discounts' }]}
                // ButonComponent={<BreadCrumButton onClick={() => push('/discounts')} />}
            />

            <div className="mb-8 rounded-lg bg-white p-6">
                <div className="grid grid-cols-1 items-end gap-4 md:grid-cols-3">
                    {/* Search input */}
                    <div className="w-full">
                        <SearchInput onChange={handleSearchChange} value={searchTerm} placeholder="Search discount..." />
                    </div>

                    {/* Dropdown */}
                    <div className="w-full">
                        <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown} initail={selectedStatus} setSelectedStatus={setSelectedStatus} />
                    </div>

                    {/* Add Button */}
                    <div className="flex justify-end">
                        <button
                            onClick={() => {
                                setEditDiscount(null);
                                setShowAddModal(true);
                            }}
                            className="h-12 rounded-md bg-[#1D7EB6] px-4 text-sm font-medium text-white hover:bg-[#155a8a]"
                        >
                            Add Discount
                        </button>
                    </div>
                </div>
            </div>

            <div className="px-4 pt-5 lg:px-5">
                <div className="bg-white">
                    <div className="overflow-x-auto">
                        <table className={`w-full min-w-[1200px] border-collapse font-inter ${loader ? 'pointer-events-none opacity-50' : ''}`}>
                            <thead className="bg-[#e4e4e4]">
                                <tr>
                                    {columns.map((col) => (
                                        <th key={col.key} className={`px-4 py-3 text-left text-sm font-semibold ${col.width}`}>
                                            {col.label}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {discounts.map((d) => (
                                    <tr key={d.id} className="border-b hover:bg-gray-50">
                                        <td className="px-4 py-3 text-sm text-gray-700">{d.name}</td>
                                        <td className="px-4 py-3 text-sm capitalize text-gray-700">{d.type}</td>
                                        <td className="px-4 py-3 text-sm text-gray-700">{d.value}</td>
                                        <td className="px-4 py-3 text-sm text-gray-700">{new Date(d.created_at).toLocaleDateString()}</td>
                                        <td className="px-4 py-3 text-sm">
                                            <span
                                                className={`inline-block rounded-full px-3 py-1 text-xs font-medium ${
                                                    d.status_name?.toLowerCase() === 'active' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                                                }`}
                                            >
                                                {d.status_name}
                                            </span>
                                        </td>
                                        <td className="h-[56px] px-4 text-right align-middle">
                                            <button
                                                onClick={() => {
                                                    setEditDiscount(d);
                                                    setShowAddModal(true);
                                                }}
                                                className="mr-2 inline-flex items-center gap-2 rounded border border-[#e4e4e4] bg-white px-3 py-2 font-medium text-[#2d2d2e] transition-all hover:bg-neutral-100"
                                            >
                                                <EditIcon />
                                            </button>
                                            <button
                                                onClick={() => setDeleteTarget(d)}
                                                className="inline-flex items-center gap-2 rounded border border-[#e4e4e4] bg-white px-3 py-2 font-medium text-[#993333] transition-all hover:bg-neutral-100"
                                            >
                                                <TrashIcon />
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>

                        {loader && (
                            <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-60">
                                <Loading />
                            </div>
                        )}
                    </div>
                </div>

                {/* Pagination */}
                {discounts?.length !== 0 && !loader && (
                    <div className="flex flex-col items-center gap-4 border-t border-[#E4E4E4] p-4 md:flex-row md:justify-between">
                        {/* ← → & page numbers */}
                        <div className="flex items-center gap-2">
                            {/* Previous */}
                            <button onClick={() => setPageSafe(pagination.page - 1)} disabled={pagination.page === 1} className="rounded-md p-2 disabled:opacity-50">
                                <PaginationRightIcon />
                            </button>

                            {/* Page numbers */}
                            <div className="flex space-x-1">
                                {getPaginationRange(pagination.page - 1, totalPages).map((i) => (
                                    <button
                                        key={i}
                                        onClick={() => setPageSafe(i + 1)}
                                        className={`rounded-md px-3 py-1 ${pagination.page === i + 1 ? 'bg-[#1D7EB6] text-white' : 'hover:bg-gray-100'}`}
                                    >
                                        {i + 1}
                                    </button>
                                ))}
                            </div>

                            {/* Next */}
                            <button onClick={() => setPageSafe(pagination.page + 1)} disabled={pagination.page === totalPages} className="rounded-md p-2 disabled:opacity-50">
                                <PaginationLeftIcon />
                            </button>
                        </div>

                        {/* Page size selector */}
                        <div className="relative flex items-center gap-2">
                            <span className="text-sm text-gray-500">Showing</span>

                            <div className="relative">
                                <button onClick={() => setShowDropdown((prev) => !prev)} className="flex items-center gap-1 rounded-md border border-gray-200 bg-[#EDF5F9] px-2 py-1 text-sm">
                                    {pagination.pageSize}
                                    <PaginationDownIcon />
                                </button>

                                {showDropdown && (
                                    <div className={`absolute left-0 z-10 w-16 rounded-md border border-gray-200 bg-white shadow-lg ${dropUp ? 'bottom-full mb-2' : 'top-full mt-2'}`}>
                                        <div className="py-1">
                                            {[10, 20, 50].map((value) => (
                                                <button
                                                    key={value}
                                                    className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                                    onClick={() => {
                                                        setPagination((prev) => ({ ...prev, page: 1, pageSize: value }));
                                                        setShowDropdown(false);
                                                    }}
                                                >
                                                    {value}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            <span className="text-sm text-gray-500">Discounts out of {pagination.total}</span>
                        </div>
                    </div>
                )}
            </div>

            <AddAndUpdateDiscountPopup
                isOpen={showAddModal}
                onClose={() => {
                    setShowAddModal(false);
                    setEditDiscount(null);
                }}
                onSubmit={handleSubmitDiscount}
                initialValues={editDiscount || undefined}
                isEdit={!!editDiscount}
                isSubmitting={isSubmitting}
            />

            {deleteTarget && (
                <Modal isOpen={true} onClose={() => setDeleteTarget(null)} classes="!max-w-md">
                    <div className=" p-6">
                        <h2 className="mb-4 text-lg font-semibold text-gray-800">Confirm Deletion</h2>
                        <p className="mb-6 text-sm text-gray-600">
                            Are you sure you want to delete <strong>{deleteTarget.name}</strong>? This action cannot be undone.
                        </p>
                        <div className="flex justify-end gap-3">
                            <button onClick={() => setDeleteTarget(null)} className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                                Cancel
                            </button>
                            <button
                                onClick={handleDeleteDiscount}
                                disabled={isDeleting}
                                className="flex items-center gap-2 rounded bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50"
                            >
                                {isDeleting ? 'Deleting...' : 'Delete'}
                            </button>
                        </div>
                    </div>
                </Modal>
            )}
        </DefaultPageLayout>
    );
};

export default DiscountManagementLayout;
