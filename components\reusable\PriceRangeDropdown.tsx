'use client';

import { useState } from 'react';

export default function PriceRangeDropdown() {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');

  const handleApply = () => {
    setIsDropdownOpen(false); // Close dropdown after applying
  };

  const handleReset = () => {
    setMinPrice('');
    setMaxPrice('');
  };

  return (
    <div className="relative w-full font-inter h-[100%]">
      {/* Dropdown Button */}
      <button
        className="w-full border h-[100%] p-3 rounded-md flex justify-between items-center text-gray-600"
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
      >
        {minPrice && maxPrice ? `${minPrice} to ${maxPrice}` : 'Select Price Range'}
        <svg
          className={`h-4 w-4 transform transition-transform ${isDropdownOpen ? 'rotate-180' : 'rotate-0'}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Content */}
      {isDropdownOpen && (
        <div className="absolute w-full mt-2 bg-white border shadow-lg rounded-lg z-10 p-3">
          {/* Min & Max Inputs */}
          <div className="flex space-x-2">
            <input
              type="number"
              placeholder="Select Minimum"
              value={minPrice}
              onChange={(e) => setMinPrice(e.target.value)}
              className="w-1/2 p-2 border rounded-md text-gray-600 focus:outline-none"
            />
            <input
              type="number"
              placeholder="Select Maximum"
              value={maxPrice}
              onChange={(e) => setMaxPrice(e.target.value)}
              className="w-1/2 p-2 border rounded-md text-gray-600 focus:outline-none"
            />
          </div>

          {/* Reset & Apply Buttons */}
          <div className="flex justify-between mt-3">
            <button className="text-[#1d7eb6] w-full" onClick={handleReset}>
              Reset
            </button>
            <button
              className="bg-[#1d7eb6] text-white w-full px-4 py-2 rounded-md"
              onClick={handleApply}
            >
              Apply
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
