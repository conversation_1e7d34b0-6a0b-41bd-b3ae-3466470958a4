'use client';

import type React from 'react';
import { ActionIcon, DatatableAccendingSortingIcon, DatatableDeccendingSortingIcon, DatatableSortingIcon, PaginationDownIcon, PaginationLeftIcon, PaginationRightIcon } from '@/components/icon/Icon';
import { useState, useRef, useEffect } from 'react';
import StatusBadge from '@/components/reusable/StatusBandage';
import Link from 'next/link';
import Modal from '@/components/reusable/modals/modal';
import LetsConnectModel from '@/components/reusable/modals/LetsConnectModel';
import CreateEventModel from '@/components/reusable/modals/CreateEventModel';
import UpdateLeadStatusModel from '@/components/reusable/modals/UpdateLeadStatusMode';
import SuccessfullyDeleted from '@/components/reusable/modals/SuccessfullyDeleted';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import Loading from '@/components/layouts/loading';
import SearchInput from '@/components/reusable/SearchBar';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import { useSearchParams } from 'next/navigation';
import { AGENT_ACCOUNT_TYPE } from '@/store/utils.ts/types/AgentProfile';

const dropdown = [{ label: 'All' }, { label: 'Pending' }, { label: 'Activated' }, { label: 'Rejected' }, { label: 'Incomplete' }, { label: 'Suspended' }];

const dropdownAccountTypes = [{ label: 'All' }, { label: 'Agents' }, { label: 'Agencies' }];

const columns = [
    { key: 'id', label: 'S. No', width: 'w-[6%]', showOnMobile: false },
    { key: 'name', label: 'Name', width: 'w-[10%]', showOnMobile: true },
    { key: 'location', label: 'Email', width: 'w-[14%]', showOnMobile: true },
    { key: 'contact', label: 'Contact', width: 'w-[12%]', showOnMobile: true },
    { key: 'accounttype', label: 'Account Type', width: 'w-[13%]', showOnMobile: false },
    { key: 'subscriptiondate', label: 'Subscription Date', width: 'w-[13%]', showOnMobile: false },
    { key: 'renewaldate', label: 'Renewal Date', width: 'w-[10%]', showOnMobile: false },
    { key: 'package', label: 'Package', width: 'w-[7%]', showOnMobile: false },
    { key: 'status', label: 'Status', width: 'w-[9%]', showOnMobile: true },
    { key: 'action', label: 'Action', width: 'w-[6%]', showOnMobile: true },
];

const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
    }); // e.g., "04 Jan 2025"
};
export default function LeadsContactTable() {
    const [page, setPage] = useState(0);
    const params = useSearchParams();

    let status = params.get('status');
    const [selectedStatus, setSelectedStatus] = useState(status || 'All');
    const [selectedType, setSelectedType] = useState('All');

    const [loader, setLoader] = useState(false);
    const [data, setData] = useState<any>([]);
    const [agentApplicationsPerPage, setAgentApplicationsPerPage] = useState(10);
    const totalPages = Math.ceil(data.length / agentApplicationsPerPage);
    const [showDropdown, setShowDropdown] = useState(false);
    const [showActionDropdown, setShowActionDropdown] = useState<number | null>(null);
    const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const dropdownRef = useRef<HTMLDivElement>(null);
    const actionDropdownRef = useRef<HTMLDivElement>(null);
    const tableContainerRef = useRef<HTMLDivElement>(null);
    const actionButtonRefs = useRef<Map<number, HTMLButtonElement>>(new Map());

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 300); // adjust delay as needed

        return () => {
            clearTimeout(timeoutId); // clear timeout on new keystroke
        };
    }, [searchTerm]);

    // Sorting state
    const [sortConfig, setSortConfig] = useState<{
        key: string;
        direction: 'ascending' | 'descending' | null;
    }>({
        key: '',
        direction: null,
    });

    // Handle sorting
    const requestSort = (key: string) => {
        let direction: 'ascending' | 'descending' | null = 'ascending';

        if (sortConfig.key === key) {
            if (sortConfig.direction === 'ascending') {
                direction = 'descending';
            } else if (sortConfig.direction === 'descending') {
                direction = null;
            }
        }

        setSortConfig({ key, direction });
    };

    // Close dropdowns when clicking outside
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setShowDropdown(false);
            }
            if (actionDropdownRef.current && !actionDropdownRef.current.contains(event.target as Node)) {
                setShowActionDropdown(null);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Handle dropdown positioning
    const handleActionClick = (reviewId: number, event: React.MouseEvent<HTMLButtonElement>) => {
        // Store the button reference
        actionButtonRefs.current.set(reviewId, event.currentTarget);

        // Toggle dropdown
        if (showActionDropdown === reviewId) {
            setShowActionDropdown(null);
            return;
        }

        // Check position in viewport
        const buttonRect = event.currentTarget.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - buttonRect.bottom;

        // If less than 200px below (approximate dropdown height), position above
        setDropdownPosition(spaceBelow < 200 ? 'top' : 'bottom');
        setShowActionDropdown(reviewId);
    };

    const goToPage = (pageNumber: number) => setPage(pageNumber);
    const handlePrev = () => setPage((prev) => Math.max(prev - 1, 0));
    const handleNext = () => setPage((prev) => Math.min(prev + 1, totalPages - 1));

    const paginationRange = () => {
        const range = [];
        let start = Math.max(page - 1, 0);
        let end = Math.min(page + 2, totalPages - 1);

        if (end - start < 3) {
            if (start === 0) {
                end = Math.min(start + 3, totalPages - 1);
            } else {
                start = Math.max(end - 3, 0);
            }
        }

        for (let i = start; i <= end; i++) {
            range.push(i);
        }

        return range;
    };

    const getSortIcon = (key: string) => {
        if (sortConfig.key !== key) {
            return <DatatableSortingIcon />;
        }

        if (sortConfig.direction === 'ascending') {
            return <DatatableAccendingSortingIcon />;
        }

        if (sortConfig.direction === 'descending') {
            return <DatatableDeccendingSortingIcon />;
        }

        return <DatatableSortingIcon />;
    };

    const [connect, setConnect] = useState(false);
    const [createEvent, setCreateEvent] = useState(false);
    const [leadModel, setLeadModel] = useState(false);
    const [updateModalSuccess, setUpdateModalSuccess] = useState(false);

    const filteredAgentApplications = data.filter((app: any) => {
        const fullName = app?.name?.toLowerCase();
        const email = (app.email || '').toLowerCase();
        const phone = (app.phoneNumber || '').toLowerCase();
        const type = (app.accountType || '').toLowerCase();
        const created = new Date(app.created_at)
            .toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
            })
            .toLowerCase();

        const term = debouncedSearchTerm.toLowerCase();

        return fullName.includes(term) || email.includes(term) || phone.includes(term) || type.includes(term) || created.includes(term);
    });

    // Sort the data
    const sortedAgentApplications = [...filteredAgentApplications].sort((a: any, b: any) => {
        if (sortConfig.direction === null) {
            return 0;
        }

        const { key, direction } = sortConfig;

        const aValue = (a[key] || '').toString().toLowerCase();
        const bValue = (b[key] || '').toString().toLowerCase();

        if (aValue < bValue) return direction === 'ascending' ? -1 : 1;
        if (aValue > bValue) return direction === 'ascending' ? 1 : -1;
        return 0;
    });

    const fetchData = async () => {
        try {
            setData([]);

            const myHeaders = new Headers();
            setLoader(true);

            const queryParams = new URLSearchParams();

            if (selectedStatus) {
                queryParams.append('status', selectedStatus);
            }

            if (selectedType) {
                queryParams.append('accountType', selectedType);
            }

            const requestOptions: RequestInit = {
                method: 'GET',
                headers: myHeaders,
                redirect: 'follow',
                credentials: 'include',
            };
            await fetch(`${API_ENDPOINTS.GET_VERIFIED_AGENTS}?${queryParams}`, requestOptions)
                .then((response) => response.json())
                .then((result) => {
                    const formattedData = result.data.map((item: any, index: number) => ({
                        id: index + 1,
                        name:
                            !item.firstName && !item.middleName && !item.lastName
                                ? "-"
                                : `${item.firstName || ""} ${item.middleName || ""} ${item.lastName || ""}`.trim(),
                        email: item.email || "-",
                        contact: item.phone || "-",
                        accounttype: item.accountType == AGENT_ACCOUNT_TYPE.INDIVIDUAL ? "Agent" : "Agency",
                        subscriptiondate: item.createdOn ? formatDate(item.createdOn) : "-",
                        renewaldate: "-",
                        package: item.currentStatusName || "-",
                    }));
                    setData(formattedData);
                    setLoader(false);
                })
                .catch((error) => {
                    console.error(error); setLoader(false);
                });
        } catch (error) {
            console.error(error); setLoader(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, [selectedStatus, selectedType])

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    };

    return (
        <>
            {
                loader && <Loading />
            }
            <Modal classes="z-[999999]" isOpen={connect} onClose={() => setConnect(false)}>
                <LetsConnectModel onClose={() => setConnect(false)} title="Published Successfully!" desc="It is now live and visible to others." />
            </Modal>
            <Modal classes="z-[999999]" isOpen={createEvent} onClose={() => setCreateEvent(false)}>
                <CreateEventModel onClose={() => setCreateEvent(false)} />
            </Modal>
            <Modal classes="z-[999999]" isOpen={leadModel} onClose={() => setLeadModel(false)}>
                <UpdateLeadStatusModel onClose={() => setLeadModel(false)}
                    onSuccessfulDelete={() => {
                        setLeadModel(false);
                        setUpdateModalSuccess(true);
                    }}
                />
            </Modal>
            <Modal isOpen={updateModalSuccess} onClose={() => setUpdateModalSuccess(false)}>
                <SuccessfullyDeleted onClose={() => setUpdateModalSuccess(false)} title="Published Successfully! Deleted!" desc="It is now live and visible to others." />
            </Modal>

            <div className="flex items-center justify-between py-4">
                <div className="flex w-full flex-col gap-5 md:items-center md:justify-between lg:flex-row lg:gap-0">
                    <div className="flex w-full flex-col flex-wrap items-center gap-5 md:flex-row ">
                        <div className="w-full min-w-[287px] flex-1 md:max-w-[287px] ">
                            <SearchInput onChange={handleSearchChange} value={searchTerm} placeholder="Search agents..." />
                        </div>

                        <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                            <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown} initail={selectedStatus} setSelectedStatus={setSelectedStatus} />
                        </div>

                        <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                            <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdownAccountTypes} initail={selectedType} setSelectedStatus={setSelectedType} />
                        </div>
                    </div>
                </div>
            </div>

            <div className=" mt- pb-4 font-inter font-normal  leading-[30px]">
                <span className="text-[#636363]">You have </span>
                <span className="text-[#993333]">{sortedAgentApplications.length} Agent Application Requests</span>
            </div>

            <div className="bg-white  ">
                <div
                    ref={tableContainerRef}
                    className="relative rounded-lg rounded-tl-lg rounded-tr-lg border shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)]"
                >
                    <table className="w-full border-collapse font-inter">
                        <thead className="sticky top-0 z-[10]    bg-[#e4e4e4]">
                            <tr className="w-full rounded-lg rounded-tl-lg rounded-tr-lg border-none">
                                {columns.map((column) => (
                                    <th
                                        key={column.key}
                                        className={`h-[72px] cursor-pointer border-none px-4 py-4 ${column.showOnMobile ? '' : 'hidden max-md:hidden md:table-cell'
                                            } text-left font-inter text-base font-medium leading-normal text-[#2d2d2e] ${column.width}`}
                                        onClick={() => column.key !== 'action' && requestSort(column.key)}
                                    >
                                        <span className="flex items-center">
                                            {column.label}
                                            {column.key !== 'action' && getSortIcon(column.key)}
                                        </span>
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody className='max-h-[700px] overflow-y-auto max-lg:overflow-x-auto'>
                            {sortedAgentApplications?.slice(page * agentApplicationsPerPage, (page + 1) * agentApplicationsPerPage).map((review: any, index: any) => (
                                <tr key={review.id} className="text-grayText border-b border-[#E4E4E4] hover:bg-gray-50">
                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] max-md:hidden ${columns[0].width}`}>{index + 1}</td>
                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[1].width}`}>{review.name}</td>
                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#1D7EB6] ${columns[2].width}`}>{review.email}</td>
                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#1D7EB6] ${columns[3].width}`}>{review.contact}</td>
                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] max-md:hidden ${columns[4].width}`}>{review.accounttype}</td>
                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363]  max-md:hidden ${columns[5].width}`}>{review.subscriptiondate}</td>
                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363]  max-md:hidden ${columns[5].width}`}>{review.renewaldate}</td>
                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363]  max-md:hidden ${columns[5].width}`}><StatusBadge status="Standard" /></td>
                                    <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[6].width}`}>
                                        <StatusBadge status="Online" />
                                    </td>
                                    <td className={`relative px-4 py-4 ${columns[7].width}`}>
                                        <button ref={(el:any) => el && actionButtonRefs.current.set(review.id, el)} className="text-blue-500" onClick={(e) => handleActionClick(review.id, e)}>
                                            <ActionIcon />
                                        </button>
                                        {showActionDropdown === review.id && (
                                            <div
                                                ref={actionDropdownRef}
                                                className={`absolute right-0 z-[999999] w-40 rounded-md border border-gray-200 bg-white shadow-lg ${dropdownPosition === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'
                                                    }`}
                                            >
                                                <div className="py-1">
                                                    <Link href={'/dashboard/leads/leads-information'} className="block w-full px-4 py-2 text-center text-sm text-[#2D2D2E] hover:bg-gray-100">
                                                        View Proflie
                                                    </Link>
                                                    <button onClick={() => setConnect(true)} className="block w-full px-4 py-2 text-center text-sm text-[#2D2D2E] hover:bg-gray-100">
                                                        Make Admin
                                                    </button>

                                                    <button onClick={() => setCreateEvent(true)} className="block w-full px-4 py-2 text-center text-sm text-[#2D2D2E] hover:bg-gray-100">
                                                        Subscription Details
                                                    </button>
                                                    <Link href={'/dashboard/leads/leads-information'} className="block w-full px-4 py-2 text-center text-sm text-[#2D2D2E] hover:bg-gray-100">
                                                        Update Package
                                                    </Link>
                                                    <button onClick={() => setLeadModel(true)} className="block w-full px-4 py-2 text-center text-sm text-[#933] hover:bg-gray-100">Remove Access</button>
                                                </div>
                                            </div>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="flex flex-col items-center gap-4 border-t border-[#E4E4E4] p-4 md:flex-row md:justify-between">
                    <div className="flex items-center gap-2">
                        <button onClick={handlePrev} disabled={page === 0} className="rounded-md p-2 disabled:opacity-50">
                            <PaginationRightIcon />
                        </button>
                        <div className="flex space-x-1">
                            {paginationRange().map((i) => (
                                <button key={i} onClick={() => goToPage(i)} className={`rounded-md px-3 py-1 ${page === i ? 'bg-[#1D7EB6] text-white' : 'hover:bg-gray-100'}`}>
                                    {i + 1}
                                </button>
                            ))}
                        </div>
                        <button onClick={handleNext} disabled={page === totalPages - 1} className="rounded-md p-2 disabled:opacity-50">
                            <PaginationLeftIcon />
                        </button>
                    </div>
                    <div className="relative flex items-center gap-2">
                        <span className="text-sm text-gray-500">Showing</span>
                        <div className="relative" ref={dropdownRef}>
                            <button className="flex items-center gap-1 rounded-md border border-gray-200 bg-[#EDF5F9] px-2 py-1 text-sm" onClick={() => setShowDropdown(!showDropdown)}>
                                {agentApplicationsPerPage}
                                <PaginationDownIcon />
                            </button>
                            {showDropdown && (
                                <div className="absolute left-0 z-10 -mt-40 w-16 rounded-md border border-gray-200 bg-white shadow-lg">
                                    <div className="py-1">
                                        {[10, 20, 50].map((value) => (
                                            <button
                                                key={value}
                                                className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                                onClick={() => {
                                                    setAgentApplicationsPerPage(value);
                                                    setShowDropdown(false);
                                                    setPage(0); // Reset to first page when changing items per page
                                                }}
                                            >
                                                {value}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                        <span className="text-sm text-gray-500">Portfolio out of {sortedAgentApplications.length}</span>
                    </div>
                </div>
            </div>
        </>
    );
}
