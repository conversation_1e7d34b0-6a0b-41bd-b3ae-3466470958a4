import React, { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { CalenderIcon, CalenderIcon2 } from "../icon/Icon"; // Ensure the correct import path

const SelectDate = () => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  return (
    <div className="relative w-full ">
      <DatePicker
        selected={selectedDate}
        onChange={(date: Date | null) => setSelectedDate(date)}
        className="w-full px-4 py-4 pr-12 border border-gray-300 rounded-lg  focus:outline-none focus:ring-2 focus:ring-blue-500"
        placeholderText="Expiry Date*"
      />
      {/* Positioning the icon inside the input field */}
      <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
        <CalenderIcon2 />
      </div>
    </div>
  );
};

export default SelectDate;
