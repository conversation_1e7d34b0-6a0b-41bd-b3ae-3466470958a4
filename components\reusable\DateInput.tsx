import { FC, ReactNode, useRef, useState } from "react";

interface DateInputProps {
  placeholder: string;
  icon: any; // Icon is required
  classes?: string;
}

const DateInput  = ({ placeholder, icon, classes  , handleDateChange ,propValue}:any) => {
  const [value, setValue] = useState("");
  const [isActive, setIsActive] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    setIsActive(true); // Move label on top when clicked
    inputRef.current?.showPicker(); // Open date picker
  };

  return (
    <div
      className={`relative w-full border border-gray-300 rounded-md px-2 py-1 cursor-pointer flex items-center h-14 ${classes}`}
      onClick={handleClick}
    >
      {/* Floating Label */}
      <label
        className={`absolute left-0 pl-4 transition-all bg-white px-1 w-full ${
          isActive || value
            ? "text-xs -top-2  !pl-[2px] text-gray-600 left-2 !w-auto"
            : "top-1/2 transform -translate-y-1/2 text-gray-400 font-normal text-sm pr-2"
        }`}
      >
        {propValue ? propValue :placeholder}
      </label>

      {/* Custom Date Input */}
      <input
        ref={inputRef}
        type="date"
        value={value || propValue}
        onChange={(e) => {setValue(e.target.value)
handleDateChange(e.target.value) // Call the function passed from parent component to handle date change
        }}
        onBlur={() => !value && setIsActive(false)} // Move label back if no value
        placeholder=" "
        className="w-full bg-transparent h-14 focus:outline-none text-gray-500 appearance-none cursor-pointer [&::-webkit-calendar-picker-indicator]:hidden text-sm"
      />

      {/* Custom Icon */}
      <div className="absolute right-3 text-gray-500">{icon}</div>
    </div>
  );
};

export default DateInput;
