import { BASE_URL } from '@/app/lib/apiRoutes';

export interface Service {
  id: number;
  title: string;
  statusname: string;
  price: string;
  createdOn: string;
  isRemote: boolean;
  isFree: boolean;
  description: string;
}

export interface ServicesResponse {
  status: number;
  success: boolean;
  message: string;
  data: {
    items: Service[];
    pagination: {
      page: number;
      limit: number;
      total: number;
    };
  };
}

export interface ServiceNote {
  id: number;
  note: string;
  createdAt: string;
  adminName: string;
}

export interface ServiceHistory {
  id: number;
  action: string;
  previousStatus: number;
  newStatus: number;
  previousStatusName: string;
  newStatusName: string;
  notes: string | null;
  createdAt: string;
  adminName: string;
}

const SERVICES_API = `${BASE_URL}/api/v1/admin/services`;

const getAuthHeaders = (): HeadersInit => {
  const token = localStorage.getItem('authToken');
  if (!token) {
    console.error('No auth token found. Redirecting to login.');
    window.location.href = '/login'; // Redirect to login page
    return {};
  }
  console.log('Auth Token:', token); // Debugging token
  return { Authorization: `Bearer ${token}` };
};

const getServices = async (
  page: number,
  limit: number,
  search: string,
  statusId: number
): Promise<ServicesResponse> => {
  const headers = {
    ...getAuthHeaders(),
  } as HeadersInit;
  console.log('Request Headers:', headers); // Debugging headers

  const response = await fetch(
    `${SERVICES_API}?page=${page}&pageSize=${limit}&search=${search}&statusId=${statusId}`,
    {
      headers,
    }
  );

  if (response.status === 401) {
    console.error('Unauthorized access - 401. Redirecting to login.');
    localStorage.removeItem('authToken'); // Clear invalid token
    window.location.href = '/login'; // Redirect to login page
    throw new Error('Unauthorized access');
  }

  return response.json();
};

const deleteService = async (id: number): Promise<{ success: boolean; message: string }> => {
  const response = await fetch(`${SERVICES_API}/${id}`, {
    method: 'DELETE',
    headers: {
      ...getAuthHeaders(),
    } as HeadersInit,
  });
  return response.json();
};

const updateServiceStatus = async (
  id: number,
  statusId: number
): Promise<{ success: boolean; message: string }> => {
  const response = await fetch(`${SERVICES_API}/${id}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      ...getAuthHeaders(),
    } as HeadersInit,
    body: JSON.stringify({ statusId }),
  });
  return response.json();
};

const addServiceNote = async (
  id: number,
  note: string
): Promise<{ success: boolean; message: string }> => {
  const response = await fetch(`${SERVICES_API}/${id}/notes`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...getAuthHeaders(),
    } as HeadersInit,
    body: JSON.stringify({ note }),
  });
  return response.json();
};

const serviceService = {
  getServices,
  deleteService,
  updateServiceStatus,
  addServiceNote,
};

export default serviceService;
