"use client"

import { CloseIconModel } from "@/components/icon/Icon"

 

const ReferralDetails = ({ onClose, referal }: any) => {
  const handleClose = () => {
    onClose(true)
  }

  const fullName =
    `${referal?.user_firstname || ""} ${referal?.user_middlename || ""} ${referal?.user_lastname || ""}`.trim()
  const subscriptionPrice = Number.parseFloat(referal?.subscription_price?.toString() || "0").toFixed(2)
  const commissionAmount = Number.parseFloat(referal?.commissionAmount?.toString() || "0").toFixed(2)
  const commissionRate = `${Number.parseFloat(referal?.salesperson_commission_rate?.toString() || "0")}%`

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toISOString().split("T")[0]
    } catch {
      return "Invalid Date"
    }
  }

  const referralDate = formatDate(referal?.referred_date)
  const conversionDate = formatDate(referal?.subscription_startdate)

  return (
    <div className="w-full   ">
      <div className="relative flex items-center justify-end  ">
        <button
          className="cursor-pointer hover:bg-gray-100 p-2 rounded-full transition-colors"
          onClick={handleClose}
          aria-label="Close modal"
        >
          <CloseIconModel />
        </button>
      </div>

      <div className="flex flex-col items-center">
        <h2 className="text-2xl font-semibold text-[#993333] mt-4 mb-6">
          Referral Details - {referal?.salesperson_referral_id}
        </h2>
      </div>

      <div className="text-[#333] space-y-6 text-sm">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-bold text-base mb-3 text-gray-700">Client Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <span className="font-medium text-gray-600">Name:</span>
              <p className="text-gray-900">{fullName || "N/A"}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Email:</span>
              <p className="text-gray-900">{referal?.user_email || "N/A"}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-bold text-base mb-3 text-gray-700">Salesperson Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <span className="font-medium text-gray-600">Name:</span>
              <p className="text-gray-900">{referal?.salesperson_name || "N/A"}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Referral ID:</span>
              <p className="text-gray-900">{referal?.salesperson_referral_id || "N/A"}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-bold text-base mb-3 text-gray-700">Subscription Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <span className="font-medium text-gray-600">Plan:</span>
              <div className="mt-1">
                <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-xs font-medium">
                  {referal?.subscription_plan_name || "N/A"}
                </span>
              </div>
            </div>
            <div>
              <span className="font-medium text-gray-600">Value:</span>
              <p className="text-gray-900 font-semibold">AED {subscriptionPrice}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-bold text-base mb-3 text-gray-700">Commission Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <span className="font-medium text-gray-600">Commission Rate:</span>
              <p className="text-gray-900 font-semibold">{commissionRate}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Commission Amount:</span>
              <p className="text-gray-900 font-semibold">AED {commissionAmount}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-bold text-base mb-3 text-gray-700">Status & Timeline</h3>
          <div className="space-y-3">
            <div>
              <span className="font-medium text-gray-600">Current Status:</span>
              <div className="mt-1">
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium">
                  {referal?.commission_status_name  }
                </span>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <span className="font-medium text-gray-600">Referral Date:</span>
                <p className="text-gray-900">{referralDate}</p>
              </div>
              <div>
                <span className="font-medium text-gray-600">Conversion Date:</span>
                <p className="text-gray-900">{conversionDate}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReferralDetails
