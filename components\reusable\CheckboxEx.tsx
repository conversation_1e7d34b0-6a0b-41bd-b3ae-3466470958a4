import React from "react";

interface NoExpiryCheckboxProps {
  onChange: (checked: boolean) => void;
}

const NoExpiryCheckbox: React.FC<NoExpiryCheckboxProps> = ({ onChange }) => {
  return (
    <label className="flex items-center space-x-2 cursor-pointer">
      <input
        type="checkbox"
        className="w-4 h-4"
        onChange={(e) => onChange(e.target.checked)}
      />
      <span className="text-[#636363] font-inter font-normal">No Expiry</span>
    </label>
  );
};

export default NoExpiryCheckbox;
