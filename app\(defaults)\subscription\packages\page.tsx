'use client';

import React from 'react';
import Sidebar from '@/components/layouts/sidebar';
import Header from '@/components/layouts/header';
 
import DefaultPageLayout from '@/components/layouts/defaultPageLayout';
import BreadCrums from '@/components/reusable/BreadCrums';
import PackagesFeatureConfigurator from '@/app/subscription/packagesComponent/PackagesFeatureConfigurator';

export default function PackagesPage() {
  return (
    <>

         <DefaultPageLayout>
            <BreadCrums
                mainHeading="Package & Feature"
                breadcrumbs={[{ text: 'Dashboard', url: '/' }, { text: 'Package & Feature' }]}
                // ButonComponent={
                //     <BreadCrumButton
                //         onClick={() => {
                //             push('/advertising');
                //         }}
                //     />
                // }
            />
           
  
  
        <main className="p-4  ">
          <PackagesFeatureConfigurator />
        </main>
  
        </DefaultPageLayout> 
   
    </>
  );
} 
