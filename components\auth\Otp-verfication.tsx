'use client';
import { showMessage } from '@/app/lib/Alert';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import { ResendCodeIcon } from '../icon/refresh-con';
import { EyeIcon, EyeIconPassword } from '../icon/Icon';

const OtpVerfication = () => {
    const { push, back } = useRouter();
    const [digits, setDigits] = useState(['', '', '', '']);
    const [loading, setLoading] = useState(false);
    const [timer, setTimer] = useState(60);
    const email = localStorage.getItem('tempmail');
    const [timerCount, setTimerCount] = useState(false);
    const [password, setPassword] = React.useState('');
    const [confirmPasswod, setConfirmPassword] = React.useState('');
    const [showPassword, setShowPassword] = React.useState(false);
    const [isResetPassword, setIsResetPassword] = React.useState(false);

    const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);

    const doPasswordsMatch = (password: string, confirmPassword: string) => {
        return password === confirmPassword;
    };

    const PasswordRules = ({ password }: { password: string }) => {
        const rules = [
            { test: /.{8,}/, message: 'At least 8 characters' },
            { test: /[A-Z]/, message: 'At least one uppercase letter' },
            { test: /[a-z]/, message: 'At least one lowercase letter' },
            { test: /\d/, message: 'At least one number' },
            { test: /[@$!%*?&]/, message: 'At least one special character' },
        ];

        if (password.length === 0) return null;

        const firstUnmetRule = rules.find((rule) => !rule.test.test(password));

        return firstUnmetRule ? <div className="mt-2 text-red-500">{firstUnmetRule.message}</div> : <div></div>;
    };
    const handleChange2 = (index: number, value: string) => {
        if (/^\d?$/.test(value)) {
            const newDigits = [...digits];
            newDigits[index] = value;
            setDigits(newDigits);

            if (value && index < 3) {
                const nextInput = document.getElementById(`digit-${index + 1}`);
                nextInput?.focus();
            }
        }
    };

    // const handleVerifyOtpAccountCreation = async () => {
    //     try {
    //         const response = await fetch(API_ENDPOINTS.REGISTER_OTP_VERIFICATION, {
    //             method: 'POST',
    //             headers: {
    //                 'Content-Type': 'application/json',
    //             },
    //             credentials: 'include',
    //             body: JSON.stringify({
    //                 email,
    //                 otp: digits.join(''),
    //             }),
    //         });

    //         const result = await response.json();
    //         if (result.success) {
    //             showMessage('OTP verified successfully!', 'success');
    //             localStorage.removeItem('tempmail');
    //             push('/login');
    //         } else {
    //             showMessage(result.message, 'error');
    //         }
    //     } catch (error) {
    //         console.error(error);
    //         showMessage('Something went wrong.', 'error');
    //     }
    // };

    // const handleResendOtp = async () => {
    //     try {
    //         setLoading(true);

    //         const response = await fetch(API_ENDPOINTS.RESEND_REGISTER_OTP, {
    //             method: 'POST',
    //             headers: {
    //                 'Content-Type': 'application/json',
    //             },
    //             credentials: 'include',
    //             body: JSON.stringify({
    //                 email,
    //             }),
    //         });

    //         const result = await response.json();
    //         if (result.success) {
    //             showMessage('OTP resent successfully!', 'success');
    //             setTimer(60); // Start 60 second timer after resend
    //         } else {
    //             showMessage(result.message || 'Failed to resend OTP.', 'error');
    //         }
    //     } catch (error) {
    //         console.error(error);
    //         showMessage('Something went wrong.', 'error');
    //     } finally {
    //         setLoading(false);
    //     }
    // };

    // const handleSendForNewPasswordForReset = () => {
    //     try {
    //         const myHeaders = new Headers();
    //         myHeaders.append('Content-Type', 'application/json');

    //         const raw = JSON.stringify({
    //             email,
    //             newPassword: password,
    //             confirmPassword: confirmPasswod,
    //         });

    //         const requestOptions: RequestInit = {
    //             method: 'POST',
    //             headers: myHeaders,
    //             body: raw,
    //             redirect: 'follow',
    //             credentials: 'include',
    //         };

    //         fetch(API_ENDPOINTS.RESET_PASSWORD, requestOptions)
    //             .then((response) => response.json())
    //             .then((result) => {
    //                 if (result.success) {
    //                     showMessage(result?.message, 'success');
    //                     push('/login');
    //                 } else {
    //                     showMessage(result?.message, 'error');
    //                 }
    //             })
    //             .catch((error) => {
    //                 console.error(error);
    //                 showMessage('Something went wrong', 'error');
    //             });
    //     } catch (error) {
    //         console.log(error);
    //         showMessage('Something went wrong', 'error');
    //     }
    // };

    useEffect(() => {
        if (!email) {
            back();
        }
    }, []);

    useEffect(() => {
        setTimeout(() => {
            if (timer > 0) {
                setTimer(timer - 1);
            }
        }, 1000);
    }, [timerCount, timer]);

    return (
        <div>
            <div className="pb-5">
                <div className="py-2 text-center font-golosText">
                    <span className="text-[26px] font-semibold text-[#993333]">Enter</span>
                    <span className="text-[26px] font-semibold text-[#2d2d2e]"> Confirmation Code</span>
                </div>
                <div className="text-center font-inter text-base font-normal leading-normal text-[#636363]">
                    Enter the 4-digit OTP we sent to your provided email address
                    <span className="font-semibold text-[#2D2D2E]"> {email}</span>
                </div>
            </div>
            {isResetPassword ? (
                <div className="space-y-4">
                    <div className="relative">
                        <input
                            type={showPassword ? 'text' : 'password'}
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder="Enter Password"
                            required
                            className="text-black-200  h-[53px]  w-full self-stretch  rounded-lg border border-[#e4e4e4] px-5 font-inter focus:outline-blue-500"
                        />
                        <div className="flex absolute right-3 top-5 w-fit items-center justify-end">
                            <span onClick={() => setShowPassword(!showPassword)} className="cursor-pointer font-inter text-base font-medium leading-tight text-[#636363]">
                                {' '}
                                {showPassword ? <EyeIconPassword /> : <EyeIcon />}
                            </span>
                        </div>
                        <PasswordRules password={password} />
                    </div>
                    <div className="relative">
                        <input
                            type={showConfirmPassword ? 'text' : 'password'}
                            value={confirmPasswod}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            placeholder="Re Enter Password"
                            required
                            className="text-black-200  h-[53px]  w-full self-stretch rounded-lg border border-[#e4e4e4] px-5 font-inter focus:outline-blue-500"
                        />
                        <div className="flex absolute right-3 top-5 w-fit items-center justify-end">
                            <span onClick={() => setShowConfirmPassword(!showConfirmPassword)} className="cursor-pointer font-inter text-base font-medium leading-tight text-[#636363]">
                                {' '}
                                {showConfirmPassword ? <EyeIconPassword /> : <EyeIcon />}
                            </span>
                        </div>
                        {confirmPasswod && (
                            <p className={doPasswordsMatch(password, confirmPasswod) ? 'hidden' : 'text-xs text-red-500'}>
                                {doPasswordsMatch(password, confirmPasswod) ? 'Passwords match' : 'Passwords do not match'}
                            </p>
                        )}
                    </div>

                    {/* <button onClick={handleSendForNewPasswordForReset} className="flex h-14 w-full items-center justify-center self-stretch rounded-lg bg-[#1d7eb6]">
                        <span className="text-lg font-medium text-white">Continue</span>
                    </button> */}
                </div>
            ) : (
                <div className="space-y-4">
                    <div className="flex justify-center gap-2">
                        {digits.map((digit, index) => (
                            <input
                                key={index}
                                id={`digit-${index}`}
                                type="text"
                                maxLength={1}
                                placeholder="_"
                                value={digit || ''}
                                onChange={(e) => handleChange2(index, e.target.value)}
                                className="text-black-200 right-1 h-12 w-20 rounded-md border border-[#********] text-center text-xl"
                            />
                        ))}
                    </div>

                    {/* {digits.every((digit) => digit !== '') && (
                        <div className="pt-10">
                            <button
                                onClick={handleVerifyOtpAccountCreation}
                                className="inline-flex h-14 !w-full !cursor-pointer items-center justify-center gap-2.5 self-stretch rounded-lg bg-[#1d7eb6] px-8 py-4"
                            >
                                <span className="flex items-center justify-center gap-[3px] rounded p-[3px] text-center font-inter text-lg font-normal leading-normal text-white">Verify</span>
                            </button>
                        </div>
                    )}

                    <div className="text-grayText flex mt-2 justify-center gap-4 pt-10 text-center font-inter text-base">
                        <button type="button" onClick={handleResendOtp} disabled={loading || timer > 0} className="font-medium text-[#993333]">
                            {loading ? 'Resending...' : timer > 0 ? `Resend in ${timer}s` : 'Resend Code'}
                        </button>
                    </div> */}
                </div>
            )}

            <div className=" absolute bottom-5  ">
                <div className="flex -flex items-center  justify-center gap-1.5  ps-10">
                    <p className="text-base font-medium leading-tight  text-[#636363]">Already have an account?</p>

                    <button onClick={() => push('/login')} className="text-center text-lg font-medium leading-normal text-[#1d7eb6]">
                        Log In
                    </button>
                </div>
            </div>
        </div>
    );
};

export default OtpVerfication;
