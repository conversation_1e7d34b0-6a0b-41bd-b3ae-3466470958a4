import React, { type FC, type ReactNode } from "react";

interface InputProps {
  id: string;
  label?: string;
  type?: string;
  placeholder?: string;
  className?: string;
  icon?: ReactNode;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  value?: string;
  disable?: boolean;
  required?: boolean;
}

const LabelInput: FC<InputProps> = ({
  id,
  label,
  placeholder,
  icon,
  onChange,
  value,
  className = "",
  type = "text",
  disable,
  required
}) => {
  return (
    <div className="relative">
      {label ? (
        <label
          htmlFor={id}
          className="absolute left-3 top-1/2 -translate-y-1/2 text-xs text-gray-500 font-manrope px-1 z-10"
        >
          {label}
        </label>

      ) : (
        ""
      )}
      <div className="relative">
        <input
          id={id}
          name={id}
          type={type}
          placeholder={placeholder}
          className={`px-4 py-2 h-12 rounded-lg border text-black focus:outline-none font-normal w-full text-sm ${className}`}
          onChange={onChange}
          value={value}
          disabled={disable}
          required={required}
        />
        {icon ? (
          <span className="absolute end-5 top-1/2 -translate-y-1/2">
            {icon}
          </span>
        ) : (
          ""
        )}
      </div>
    </div>
  );
};

export default LabelInput;