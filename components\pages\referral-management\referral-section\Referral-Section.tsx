import React, { useEffect, useRef, useState, useCallback } from 'react'
import SearchDropDown from '../SearchDropDown';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import Modal from '@/components/reusable/modals/modal';
import ReferalDetails from '@/components/reusable/modals/ReferalDetails';

 interface Referral {
    id: number;
    user_firstname: string;
    user_middlename: string;
    user_lastname: string;
    user_email: string;
    salesperson_name: string;
    salesperson_referral_id: string;
    salesperson_commission_rate: number;
    subscription_plan_name: string;
    subscription_price: number;
    commissionAmount: number;
    status: number;
    referred_date: string;
    paidAt: string;
    createdAt: string;
    updatedAt: string;
    adminApproval :boolean;
    commission_status_name: string;
    commission_id: number
  }
 
const ReferralSection = () => {
     const [activeTab, setActiveTab] = useState('overview');
        const [searchTerm, setSearchTerm] = useState('');
        const [statusFilter, setStatusFilter] = useState('All Status');
        const [plansFilter, setPlansFilter] = useState('All Plans');
        const [monthFilter, setMonthFilter] = useState('All Months');
      
        const[referralData , setReferralData] = useState<Referral[]>([])
        const [loading, setLoading] = useState(true);
        const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
        const [notification, setNotification] = useState<{ message: string; subMessage: string } | null>(null);
    
        // Pagination states for server-side pagination
        const [currentPage, setCurrentPage] = useState(1);
        const [itemsPerPage, setItemsPerPage] = useState(10);
        const [totalCount, setTotalCount] = useState(0);
        const [totalPages, setTotalPages] = useState(0);
    
        const dropdownRef = useRef<HTMLDivElement>(null);
        const [isStatusOpen, setIsStatusOpen] = useState(false);
        const [isMonthOpen, setIsMonthOpen] = useState(false);
        const statusDropdownRef = useRef<HTMLDivElement>(null);
        const monthDropdownRef = useRef<HTMLDivElement>(null);
        const [connect, setConnect] = useState(false);
        const [selectedReferal, setSelectedReferal] = useState<Referral | null>(null);
        const [monthDropdownOptions , setMonthDropdownOptions] = useState<string[]>([]);
        const [plansDropdownOptions, setPlansDropdownOptions] = useState<string[]>([]); 
        // Close dropdowns when clicking outside
        useEffect(() => {
            function handleClickOutside(event: MouseEvent) {
                if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target as Node)) {
                    setIsStatusOpen(false);
                }
                if (monthDropdownRef.current && !monthDropdownRef.current.contains(event.target as Node)) {
                    setIsMonthOpen(false);
                }
            }
    
            document.addEventListener('mousedown', handleClickOutside);
            return () => document.removeEventListener('mousedown', handleClickOutside);
        }, []);
    
        // Debounced search term to prevent too many API calls
        const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
        
        // Debounce search input
        useEffect(() => {
            const timer = setTimeout(() => {
                setDebouncedSearchTerm(searchTerm);
            }, 500);
            return () => clearTimeout(timer);
        }, [searchTerm]);

        // Auto-hide notification after 3 seconds
        useEffect(() => {
            if (notification) {
                const timer = setTimeout(() => {
                    setNotification(null);
                }, 3000);
                return () => clearTimeout(timer);
            }
        }, [notification]); 
        const handleActionClick = (personId: number, event: React.MouseEvent) => {
            event.stopPropagation();
            setActiveDropdown(activeDropdown === personId ? null : personId);
         
        };
      
        // Define dropdown options
        const statusDropdownOptions = [{ label: 'All Status' }, { label: 'Paid' }, { label: 'Pending' }, { label: 'Cancelled' }];
     
        // Add new handlers for referral actions
        const handleViewReferralDetails = (referral: Referral) => {
            setActiveDropdown(null);
            setNotification({
                message: 'View Details',
                subMessage: `Viewing details for referral ${referral.user_firstname} ${referral.user_lastname}`,
            });
            setSelectedReferal(referral);
            setConnect(true);
        };

        // Pagination handlers for server-side pagination
        const handlePageChange = (page: number) => {
            setCurrentPage(page);
        };

        const handleItemsPerPageChange = (newItemsPerPage: number) => {
            setItemsPerPage(newItemsPerPage);
            setCurrentPage(1); // Reset to first page when changing items per page
        };

        // Reset pagination when filters change
        useEffect(() => {
            setCurrentPage(1);
        }, [debouncedSearchTerm, statusFilter, plansFilter, monthFilter]);
    
 
  
       const fetchreferralsData = useCallback(async () => {
                setLoading(true); 
                try {
                    const status = statusFilter === 'All Status' ? '' : statusFilter;
                    const plans = plansFilter === 'All Plans' ? '' : plansFilter;
                    const monthFilters = monthFilter === 'All Months' ? '' : monthFilter;

                    const res = await fetch(`${API_ENDPOINTS.REFERRALS}?search=${debouncedSearchTerm}&status=${status}&plan=${plans}&monthAndYear=${monthFilters}&page=${currentPage}&pageSize=${itemsPerPage}`, {
                        credentials: 'include', 
                    });
                    const result = await res.json();
        
                    if (result.success) {
                        console.log(result.data); 
                        setTotalCount(result.pagination?.total || 0);
                        setCurrentPage(result.pagination?.page || 1);
                        setItemsPerPage(result.pagination?.pageSize || 10);
                        setTotalPages(Math.ceil((result.pagination?.total || 0) / (result.pagination?.pageSize || 10)));
                        setMonthDropdownOptions(['All Months', ...(result.availableMonths || [])]);
                        setPlansDropdownOptions(['All Plans', ...(result.availablePlans || [])]);
                        setReferralData(result.data || []);
                    } else {
                        console.error('Failed to fetch referrals:', result.message);
                        setReferralData([]);
                    }
                } catch (err) {
                    console.error('Failed to fetch referrals:', err);
                    setReferralData([]);
                } finally {
                    setLoading(false);
                }
            }, [debouncedSearchTerm, statusFilter, plansFilter, monthFilter, currentPage, itemsPerPage]);

        useEffect(() => {
           fetchreferralsData();
            }, [fetchreferralsData]);
    const updateReferal =async (referral: Referral , status: string  ) => {
    
    setLoading(true); 
                try {
                
                    const res = await fetch(`${API_ENDPOINTS.REFERRALS}/${referral.commission_id}`, {
                        credentials: 'include', 
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ status: status }),
                    });
                    const result = await res.json();
        
                    if (result.success) {
                        console.log(result.data); 
                        setNotification({
                            message: 'Referral Updated',
                            subMessage: `Referral has been updated successfully.`,
                        });
                        fetchreferralsData();
                    } else {
                        console.error('Failed to fetch referrals:', result.message);
                      
                    }
                } catch (err) {
                    console.error('Failed to fetch referrals:', err);
                  
                } finally {
                    setLoading(false);
                }
    
    
    }
  return (
    < >
      <div className="space-y-6">
                          {/* Header with Export */}
                          <div className="flex items-center justify-between">
                              <h2 className="text-xl font-semibold text-[#2d2d2e]">Referral Records</h2>
                              {/* <button onClick={exportToCSV} className="flex items-center gap-2 rounded-md border border-[#e4e4e4] px-4 py-2 text-sm font-medium text-[#636363] hover:bg-gray-50">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                  </svg>
                                  Export ({referralData.length})
                              </button> */}
                          </div>
      
                          {/* Filters Section */}
                          <div className="rounded-lg bg-white p-6">
                              <div className="mb-4 flex items-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#636363]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                                      />
                                  </svg>
                                  <span className="ml-2 font-medium text-[#2d2d2e]">Filters</span>
                              </div>
                              <div className="flex w-full flex-col flex-wrap items-center gap-5 md:flex-row">
                                  <div className="w-full md:min-w-[287px] flex-1 md:max-w-[287px]">
                                      <div className="relative">
                                          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                              <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                  <path
                                                      fillRule="evenodd"
                                                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                                      clipRule="evenodd"
                                                  />
                                              </svg>
                                          </div>
                                          <input
                                              type="text"
                                              placeholder="Search referrals..."
                                              className="h-14 w-full rounded-lg border border-[#e4e4e4] pl-10 pr-4 text-sm focus:border-[#1D7EB6] focus:outline-none"
                                              value={searchTerm}
                                              onChange={(e) => setSearchTerm(e.target.value)}
                                          />
                                      </div>
                                  </div>
                                  <div className="w-auto min-w-[230px]  md:min-w-[287px] flex-1 md:max-w-[287px]">
                                      <SearchDropDown classes="!h-14 w-full" dropdownOptions={statusDropdownOptions} initail="All Status" setSelectedStatus={setStatusFilter} />
                                  </div>
                                  <div className="w-auto min-w-[230px]  md:min-w-[287px] flex-1 md:max-w-[287px]">
                                      <SearchDropDown 
                                           classes="!h-14 w-full" 
                                          dropdownOptions={plansDropdownOptions.map(plan => ({ label: plan }))} 
                                          initail="All Plans" 
                                          setSelectedStatus={setPlansFilter} 
  />
                                  </div>
                                  <div className="w-auto min-w-[230px]  md:min-w-[287px] flex-1 md:max-w-[287px]">
                                      <SearchDropDown 
                                          classes="!h-14 w-full" 
                                          dropdownOptions={monthDropdownOptions.map(month => ({ label: month }))} 
                                          initail="All Months" 
                                          setSelectedStatus={setMonthFilter} 
                                      />
                                  </div>
                              </div>
                          </div>
      
                          {/* Table */}
                          <div className="rounded-lg bg-white relative   border shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)] w-full overflow-auto"> 
                              <div className="overflow-x-auto min-h-96 ">
                                  <table className="w-full  ">
                                      <thead className="bg-gray-50 text-left">
                                          <tr>
                                              <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Client</th>
                                              <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Salesperson</th>
                                              <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Plan</th>
                                              <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Value</th>
                                              <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Commission</th>
                                              <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Status</th>
                                              <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Dates</th>
                                              <th className="px-6 py-4 text-sm font-semibold text-[#636363]">Actions</th>
                                          </tr>
                                      </thead>
                                      <tbody className="divide-y divide-gray-100">
                                          {loading ? (
                                              <tr>
                                                  <td colSpan={8} className="px-6 py-12 text-center">
                                                      <div className="flex items-center justify-center">
                                                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1D7EB6]"></div>
                                                          <span className="ml-2 text-[#636363]">Loading referrals...</span>
                                                      </div>
                                                  </td>
                                              </tr>
                                          ) : referralData.length === 0 ? (
                                              <tr>
                                                  <td colSpan={8} className="px-6 py-12 text-center text-[#636363]">
                                                      No referrals found
                                                  </td>
                                              </tr>
                                          ) : (
                                              referralData.map((referral: Referral, index: number) => (
                                              <tr key={index} className="hover:bg-gray-50">
                                                  <td className="px-6 py-4">
                                                      <div>
                                                         <div className="font-medium text-[#2d2d2e]">{ referral.user_firstname ? (referral.user_firstname+" "+referral.user_middlename+" "+referral.user_lastname) :"-"}</div> 
                                                          <div className="text-sm text-[#636363]">{referral.user_email}</div>
                                                      </div>
                                                  </td>
                                                  <td className="px-6 py-4">
                                                      <div>
                                                          <div className="font-medium text-[#2d2d2e]">{referral.salesperson_name}</div>
                                                          <div className="text-sm text-[#636363]">{referral.salesperson_referral_id}</div>
                                                      </div>
                                                  </td>
                                                  <td className="px-6 py-4">
                                                     {referral.subscription_plan_name && <span
                                                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                              referral.subscription_plan_name === 'Premium' ? 'bg-purple-100 text-purple-800' : 'bg-orange-100 text-orange-800'
                                                          }`}
                                                      >
                                                          {referral.subscription_plan_name}
                                                      </span>}
                                                  </td>
                                                  <td className="px-6 py-4 font-medium text-[#2d2d2e]">
                                                      AED {Number(referral.subscription_price).toFixed(2)}
                                                  </td>
                                                  <td className="px-6 py-4">
                                                      <div className="font-medium text-[#2d2d2e]">AED {Number(referral.commissionAmount).toFixed(2)}</div>
                                                      <div className="text-sm text-[#636363]">{Number(referral.salesperson_commission_rate).toFixed(2)}%</div>
                                                  </td>
                                                  <td className="px-6 py-4">
                                                     {referral.commission_status_name && <span
                                                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                              referral.commission_status_name === 'Paid'
                                                                  ? 'bg-green-100 text-green-800'
                                                                  :  referral.commission_status_name === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 
                                                                  referral.commission_status_name === 'Confirmed' ? 'bg-blue-100 text-blue-800' :
                                                                  'bg-red-100 text-red-800'
                                                          }`}
                                                      >
                                                          {referral.commission_status_name  }
                                                      </span>}
                                                  </td>
                                                  <td className="px-6 py-4">
                                                      <div className="space-y-1 text-sm">
                                                          <div>Referred: {new Date(referral.referred_date).toLocaleDateString()}</div>
                                                          {referral.paidAt && <div>Paid: {new Date(referral.paidAt).toLocaleDateString()}</div>}
                                                      </div>
                                                  </td>
                                                  <td className="relative px-6 py-4">
                                                      <button 
                                                      onClick={(e) => handleActionClick(index, e)}
                                                          title="More actions"
                                                          aria-label="More actions"
                                                          className="text-[#636363] hover:text-[#2d2d2e]">
                                                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                              <circle cx="6" cy="10" r="2" />
                                                              <circle cx="10" cy="10" r="2" />
                                                              <circle cx="14" cy="10" r="2" />
                                                          </svg>
                                                      </button>
                                                      {referral.commission_status_name && activeDropdown === index && (
                                                          <div ref={dropdownRef} className="absolute right-0 z-10 mt-2 w-48 rounded-md border border-gray-100 bg-white py-1 shadow-lg">
                                                              <button
                                                                  onClick={() => handleViewReferralDetails(referral)}
                                                                  className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                                              >
                                                                  <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                      <path
                                                                          strokeLinecap="round"
                                                                          strokeLinejoin="round"
                                                                          strokeWidth={2}
                                                                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                                                      />
                                                                  </svg>
                                                                  View Details
                                                              </button>
                                                    { referral.commission_status_name === 'Pending' &&         <button
                                                               onClick={() => updateReferal(referral, 'Confirmed')}
                                                                className="flex w-full items-center px-4 py-2 text-sm text-blue-400 hover:bg-gray-50">
                                                                  <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                      <path
                                                                          strokeLinecap="round"
                                                                          strokeLinejoin="round"
                                                                          strokeWidth={2}
                                                                          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                                                      />
                                                                  </svg>
                                                                  Confirm Referral
                                                              </button>}

                                                             {referral.commission_status_name !== 'Paid' &&          <button 
                                                              onClick={() => updateReferal(referral, 'Paid')}

                                                               className="flex w-full items-center px-4 py-2 text-sm text-green-700 hover:bg-gray-50">
                                                                  <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                      <path
                                                                          strokeLinecap="round"
                                                                          strokeLinejoin="round"
                                                                          strokeWidth={2}
                                                                          d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                                      />
                                                                  </svg>
                                                                 Paid Referral
                                                              </button>}
                                                 { referral.commission_status_name !== 'Cancelled' && referral.commission_status_name !== 'Paid' &&               <button 
                                                              onClick={() => updateReferal(referral, 'Cancelled')}

                                                               className="flex w-full items-center px-4 py-2 text-sm text-red-700 hover:bg-gray-50">
                                                                  <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                      <path
                                                                          strokeLinecap="round"
                                                                          strokeLinejoin="round"
                                                                          strokeWidth={2}
                                                                          d="M6 18L18 6M6 6l12 12"
                                                                      />
                                                                  </svg>
                                                                 Cancel Referral
                                                              </button>}
                                                          </div>
                                                      )}
                                                  </td>
                                              </tr>
                                          ))
                                          )}
                                      </tbody>
                                  </table>
                              </div>
      
                              {/* Pagination Controls */}
                             
                          </div>
 <div className="flex max-md:flex-col items-center justify-between border-t border-gray-200 px-6 py-3 max-md:gap-5">
                                  <div className="flex items-center gap-2 justify-start">
                                      <span className="text-sm text-gray-700">Show</span>
                                      <select 
                                          value={itemsPerPage} 
                                          onChange={(e) => handleItemsPerPageChange(Number(e.target.value))} 
                                          className="rounded border border-gray-300 px-2 py-1 text-sm"
                                          title="Entries per page"
                                          aria-label="Entries per page"
                                      >
                                          {[10, 20, 30].map((option) => (
                                              <option key={option} value={option}>
                                                  {option}
                                              </option>
                                          ))}
                                      </select>
                                      <span className="text-sm text-gray-700">entries</span>
                                  </div>
      
                               <div className="flex items-center gap-2">
                                                          <div className="flex gap-1">
                                                              <button
                                                                  onClick={() => handlePageChange(currentPage - 1)}
                                                                  disabled={currentPage === 1}
                                                                  className={`px-3 py-1 text-sm ${
                                                                      currentPage === 1 ? 'cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'
                                                                  }`}
                                                              >
                                                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                                                  </svg>
                                                              </button>
                                                              {/* Responsive pagination: show first, last, current, and neighbors */}
                                                              {totalPages >1 && (
                                                                  <>
                                                                      {currentPage > 2 && (
                                                                          <>
                                                                              <button
                                                                                  onClick={() => handlePageChange(1)}
                                                                                  className={`rounded px-3 py-1 text-sm ${currentPage === 1 ? 'bg-[#1D7EB6] text-white' : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'}`}
                                                                              >
                                                                                  1
                                                                              </button>
                                                                              {currentPage > 3 && <span className="px-1 text-gray-400">...</span>}
                                                                          </>
                                                                      )}
                                                                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                                                                          .filter(
                                                                              (number) =>
                                                                                  number === 1 ||
                                                                                  number === totalPages ||
                                                                                  Math.abs(number - currentPage) <= 1
                                                                          )
                                                                          .map((number, idx, arr) => (
                                                                              <React.Fragment key={number}>
                                                                                  {number !== 1 && number !== totalPages && (
                                                                                      <button
                                                                                          onClick={() => handlePageChange(number)}
                                                                                          className={`rounded px-3 py-1 text-sm ${currentPage === number ? 'bg-[#1D7EB6] text-white' : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'}`}
                                                                                      >
                                                                                          {number}
                                                                                      </button>
                                                                                  )}
                                                                              </React.Fragment>
                                                                          ))}
                                                                      {currentPage < totalPages - 1 && (
                                                                          <>
                                                                              {currentPage < totalPages - 2 && <span className="px-1 text-gray-400">...</span>}
                                                                              <button
                                                                                  onClick={() => handlePageChange(totalPages)}
                                                                                  className={`rounded px-3 py-1 text-sm ${currentPage === totalPages ? 'bg-[#1D7EB6] text-white' : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'}`}
                                                                              >
                                                                                  {totalPages}
                                                                              </button>
                                                                          </>
                                                                      )}
                                                                  </>
                                                              )}
                                                              <button
                                                                  onClick={() => handlePageChange(currentPage + 1)}
                                                                  disabled={currentPage === totalPages}
                                                                  className={`px-3 py-1 text-sm ${
                                                                      currentPage === totalPages ? 'cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'
                                                                  }`}
                                                              >
                                                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                                                  </svg>
                                                              </button>
                                                          </div>
                                                      </div>
                              </div>
                          
                      </div>


                          <Modal classes="z-[999999]" isOpen={connect} onClose={() => setConnect(false) }>
                                      <ReferalDetails onClose={() => setConnect(false)} referal={selectedReferal}  />
                                  </Modal>
    </>
  )
}

export default ReferralSection
