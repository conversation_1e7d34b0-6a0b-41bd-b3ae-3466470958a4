'use client';
import { MinusIcon, PlusIcon } from '@/components/icon/Icon';
import { useState } from 'react';

const SpecializationForm = () => {
    const [fields, setFields] = useState(['', '']); // Start with two inputs

    const addField = () => {
        setFields([...fields, '']);
    };

    const removeField = () => {
        if (fields.length > 2) {
            setFields(fields.slice(0, -1));
        }
    };

    return (
        <div className="">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {fields.map((_, index) => (
                    <input key={index} type="text" placeholder={`Specialization ${index + 1}`} className="px-4 py-2 h-14 rounded-lg border text-black focus:outline-none font-normal w-full text-sm" />
                ))}
            </div>
            <div className="mt-4 flex justify-end gap-2">
                <button
                    onClick={removeField}
                    className="rounded-lg bg-blue-100 p-2 text-blue-500 disabled:opacity-50"
                    disabled={fields.length <= 2} 
                >
                    <div className="cursor-pointer rounded-md bg-[#D6F0FF] p-2">
                        <MinusIcon />
                    </div>
                </button>
                <button onClick={addField} className="rounded-lg bg-blue-100 p-2 text-blue-500">
                    <div className="cursor-pointer rounded-md bg-[#D6F0FF] p-2">
                        <PlusIcon />
                    </div>
                </button>
            </div>
        </div>
    );
};

export default SpecializationForm;
