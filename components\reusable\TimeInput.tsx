import { FC, ReactNode, useRef, useState } from "react";

interface TimeInputProps {
  placeholder: string;
  icon: ReactNode; // Icon is required
  classes?: string;
}

const TimeInput  = ({ placeholder, icon, classes }:any) => {
  const [value, setValue] = useState("");
  const [isActive, setIsActive] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    setIsActive(true); // Move label on top when clicked
    inputRef.current?.showPicker(); // Open time picker
  };

  return (
    <div
      className={`relative w-full border border-gray-300 rounded-md px-2 py-1 cursor-pointer flex items-center h-14 ${classes}`}
      onClick={handleClick}
    >
      {/* Floating Label */}
      <label
        className={`absolute left-0 pl-4 transition-all bg-white px-1 w-full ${
          isActive || value
            ? "text-xs -top-2 text-gray-600 left-2 !pl-[2px] !w-auto"
            : "top-1/2 transform -translate-y-1/2 text-gray-400 font-normal text-sm pr-2"
        }`}
      >
        {placeholder}
      </label>

      {/* Custom Time Input */}
      <input
        ref={inputRef}
        type="time"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onBlur={() => !value && setIsActive(false)} // Move label back if no value
        placeholder=""
        className="w-full bg-transparent focus:outline-none text-gray-500 appearance-none cursor-pointer text-sm"
      />

      {/* Custom Icon */}
      {icon && <div className="absolute right-3 text-gray-500">{icon}</div>}
    </div>
  );
};

export default TimeInput;
