'use client';

import type React from 'react';
import { ActionIcon, DatatableAccendingSortingIcon, DatatableDeccendingSortingIcon, DatatableSortingIcon, PaginationDownIcon, PaginationLeftIcon, PaginationRightIcon } from '@/components/icon/Icon';
import { useState, useRef, useEffect } from 'react';
import StatusBadge, { statusStyles } from '@/components/reusable/StatusBandage';
import Modal from '@/components/reusable/modals/modal';
import SuccessfullyDeleted from '@/components/reusable/modals/SuccessfullyDeleted';
import API_ENDPOINTS from '@/app/lib/apiRoutes';
import SearchInput from '@/components/reusable/SearchBar';
import { useRouter, useSearchParams } from 'next/navigation';
import Loading from '@/components/layouts/loading';
import SearchDropDown from '@/components/reusable/SearchDropDown';
import TextAreaInput from '@/components/reusable/TextAreaInput';
import { showMessage } from '@/app/lib/Alert';
import { AGENT_ACCOUNT_TYPE, CompanyAgentFieldLabels, DocumentCards, IndividualAgentFieldLabels } from '@/store/utils.ts/types/AgentProfile';

const columns = [
    { key: 'srno', label: 'S. No', width: 'w-[6%]' },
    { key: 'accountName', label: 'Account Name', width: 'w-[16%]' },
    { key: 'email', label: 'Email', width: 'w-[14%]' },
    { key: 'phone', label: 'Contact', width: 'w-[12%]' },
    { key: 'accountType', label: 'Ad Type', width: 'w-[10%]' },
    { key: 'createdOn', label: 'Request Date', width: 'w-[13%]' },
    { key: 'currentStatusName', label: 'Status', width: 'w-[10%]' },
    { key: 'action', label: 'Action', width: 'w-[9%]' },
];

const NoteIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" width="24" height="24" viewBox="0 0 24 24">
        <path fill="currentColor" d="M3 20.59L6.59 17H18a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2zM3 22H2V6a3 3 0 0 1 3-3h13a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3H7z" />
    </svg>
);

export default function AgentsTable() {
    const dropdown = [{ label: 'All' }, { label: 'Pending' }, { label: 'Activated' }, { label: 'Rejected' }, { label: 'Incomplete' }, { label: 'Suspended' }];

    const dropdownAccountTypes = [{ label: 'All' }, { label: 'Agents' }, { label: 'Agencies' }];

    const { push } = useRouter();
    const [page, setPage] = useState(0);
    const [loader, setLoader] = useState(false);
    const [agentApplicationsPerPage, setAgentApplicationsPerPage] = useState(10);
    const [agentApplications, setFetchedAgentApplications] = useState<any[]>([]);
    const totalPages = Math.ceil(agentApplications.length / agentApplicationsPerPage);
    const [showDropdown, setShowDropdown] = useState(false);
    const [showActionDropdown, setShowActionDropdown] = useState<number | null>(null);
    const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');
    const [updateModalReason, setUpdateModalReason] = useState(false);
    const [updateModalSuccess, setUpdateModalSuccess] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [reasonError, setReasonError] = useState('');
    const [selectedRecordId, setSelectedRecordId] = useState<number>();
    const params = useSearchParams();
    const [reasons, setReasons] = useState<any[]>([]);
    const [expandedReasonId, setExpandedReasonId] = useState<number | null>(null);
    const [documentCounts, setDocumentCounts] = useState<DocumentCards[]>([]);
    const [isPrivate, setIsPrivate] = useState(false);

    let status = params.get('status');
    const [selectedStatus, setSelectedStatus] = useState(status || 'All');
    const [selectedType, setSelectedType] = useState('All');
    const [successModalData, setSuccessModalData] = useState({
        title: '',
        description: '',
    });

    const [formValues, setFormValues] = useState({
        status: '',
        rejectionReason: '',
    });

    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const [userType, setUserType] = useState('');
    const dropdownRef = useRef<HTMLDivElement>(null);
    const actionDropdownRef = useRef<HTMLDivElement>(null);
    const tableContainerRef = useRef<HTMLDivElement>(null);
    const actionButtonRefs = useRef<Map<number, HTMLButtonElement>>(new Map());
    const [missingFields, setMissingFields] = useState<string[]>([]);

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 300); // adjust delay as needed

        return () => {
            clearTimeout(timeoutId); // clear timeout on new keystroke
        };
    }, [searchTerm]);

    // Sorting state
    const [sortConfig, setSortConfig] = useState<{
        key: string;
        direction: 'ascending' | 'descending' | null;
    }>({
        key: '',
        direction: null,
    });

    // Handle sorting
    const requestSort = (key: string) => {
        let direction: 'ascending' | 'descending' | null = 'ascending';

        if (sortConfig.key === key) {
            if (sortConfig.direction === 'ascending') {
                direction = 'descending';
            } else if (sortConfig.direction === 'descending') {
                direction = null;
            }
        }

        setSortConfig({ key, direction });
    };

    const filteredAgentApplications = agentApplications.filter((app: any) => {
        const fullName = `${app.firstName || ''} ${app.middleName || ''} ${app.lastName || ''}`.toLowerCase();
        const email = (app.email || '').toLowerCase();
        const phone = (app.phoneNumber || '').toLowerCase();
        const type = (app.accountType || '').toLowerCase();
        const created = new Date(app.created_at)
            .toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
            })
            .toLowerCase();

        const term = debouncedSearchTerm.toLowerCase();

        return fullName.includes(term) || email.includes(term) || phone.includes(term) || type.includes(term) || created.includes(term);
    });

    // Sort the data
    const sortedAgentApplications = [...filteredAgentApplications].sort((a: any, b: any) => {
        if (sortConfig.direction === null) {
            return 0;
        }

        const { key, direction } = sortConfig;

        // Special case for 'accountName' (composite of firstName + middleName + lastName)
        const getFullName = (obj: any) => `${obj.firstName || ''} ${obj.middleName || ''} ${obj.lastName || ''}`.trim().toLowerCase();

        const aValue = key === 'accountName' ? getFullName(a) : (a[key] || '').toString().toLowerCase();
        const bValue = key === 'accountName' ? getFullName(b) : (b[key] || '').toString().toLowerCase();

        if (aValue < bValue) return direction === 'ascending' ? -1 : 1;
        if (aValue > bValue) return direction === 'ascending' ? 1 : -1;
        return 0;
    });

    // Close dropdowns when clicking outside
    useEffect(() => {
        if (status) {
            setSelectedStatus(status);
        }

        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setShowDropdown(false);
            }
            if (actionDropdownRef.current && !actionDropdownRef.current.contains(event.target as Node)) {
                setShowActionDropdown(null);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Handle dropdown positioning
    const handleActionClick = (applicationId: number, event: React.MouseEvent<HTMLButtonElement>) => {
        // Store the button reference
        actionButtonRefs.current.set(applicationId, event.currentTarget);

        // Toggle dropdown
        if (showActionDropdown === applicationId) {
            setShowActionDropdown(null);
            return;
        }

        // Check position in viewport
        const buttonRect = event.currentTarget.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - buttonRect.bottom;

        // If less than 200px below (approximate dropdown height), position above
        setDropdownPosition(spaceBelow < 200 ? 'top' : 'bottom');
        setShowActionDropdown(applicationId);
    };

    const goToPage = (pageNumber: number) => setPage(pageNumber);
    const handlePrev = () => setPage((prev) => Math.max(prev - 1, 0));
    const handleNext = () => setPage((prev) => Math.min(prev + 1, totalPages - 1));

    const paginationRange = () => {
        const range = [];
        let start = Math.max(page - 1, 0);
        let end = Math.min(page + 2, totalPages - 1);

        if (end - start < 3) {
            if (start === 0) {
                end = Math.min(start + 3, totalPages - 1);
            } else {
                start = Math.max(end - 3, 0);
            }
        }

        for (let i = start; i <= end; i++) {
            range.push(i);
        }

        return range;
    };

    const getSortIcon = (key: string) => {
        if (sortConfig.key !== key) {
            return <DatatableSortingIcon />;
        }

        if (sortConfig.direction === 'ascending') {
            return <DatatableAccendingSortingIcon />;
        }

        if (sortConfig.direction === 'descending') {
            return <DatatableDeccendingSortingIcon />;
        }

        return <DatatableSortingIcon />;
    };

    const fetchData = async () => {
        try {
            setFetchedAgentApplications([]);
            const myHeaders = new Headers();
            setLoader(true);

            const queryParams = new URLSearchParams();

            if (selectedStatus) {
                queryParams.append('status', selectedStatus);
            }

            if (selectedType) {
                queryParams.append('accountType', selectedType);
            }

            const requestOptions: RequestInit = {
                method: 'GET',
                headers: myHeaders,
                redirect: 'follow',
                credentials: 'include',
            };
            await fetch(`${API_ENDPOINTS.AGENT_APPLICATIONS}?${queryParams}`, requestOptions)
                .then((response) => response.json())
                .then((result) => {
                    if (result.success) {
                        applyFilters(result.data?.profiles);

                        const profileCountsStatusWise = result?.data?.profileCounts;
                        // Total documents count
                        const total = Array.isArray(profileCountsStatusWise)
                            ? profileCountsStatusWise.reduce((sum, item) => {
                                  const count = parseInt(item?.profileCount || '0', 10);
                                  return sum + count;
                              }, 0)
                            : 0;

                        // Transform into cards
                        const documentCards: DocumentCards[] = [
                            {
                                id: 0,
                                type: 'Total Documents',
                                count: total.toString(),
                                color: '#3b82f6', // Fixed color for total
                            },
                            ...(Array.isArray(profileCountsStatusWise)
                                ? profileCountsStatusWise.map((item) => ({
                                      id: item?.statusId ?? -1,
                                      type: item?.statusName ?? 'Unknown',
                                      count: item?.profileCount ?? '0',
                                      color: statusStyles[item?.statusName]?.text,
                                  }))
                                : []),
                        ];

                        setDocumentCounts(documentCards);
                    }
                    setLoader(false);
                })
                .catch((error) => {
                    console.error(error);
                    setLoader(false);
                });
            // setFetchedAgentApplications(data);
        } catch (error) {
            console.error(error);
            setLoader(false);
        }
    };

    useEffect(() => {
        fetchData();
        if (params.get('status')) {
            setTimeout(() => {
                const url = new URL(window.location.href);
                const allParams = url.searchParams;

                // Remove specific query parameters
                allParams.delete('status');
                allParams.delete('startDate');
                allParams.delete('endDate');
                allParams.delete('role');

                // Construct the new URL
                const newUrl = `${url.pathname}${allParams.toString() ? '?' + allParams.toString() : ''}`;

                // Update URL in-place without reloading or navigation
                window.history.replaceState(null, '', newUrl);
            }, 3000);
        }
    }, [selectedStatus, selectedType]);

    const fetchReasonsForApplication = async (applicationId: number) => {
        try {
            setReasons([]);
            const response = await fetch(`${API_ENDPOINTS.AGENT_APPLICATIONS}/${applicationId}/getReasons`, {
                method: 'GET',
                credentials: 'include',
            });
            const result = await response.json();
            if (result.success) {
                setReasons(result.data.reasons || []);
            } else {
                console.error('Failed to fetch reasons:', result.message);
                setReasons([]); // Reset on error
            }
        } catch (error) {
            console.error('Error fetching reasons:', error);
            setReasons([]); // Reset on error
        }
    };

    const applyFilters = (data: any) => {
        const currentParams = new URLSearchParams(window.location.search); // fresh copy

        let filteredData = [...data];

        const startDate = currentParams.get('startDate');
        const endDate = currentParams.get('endDate');
        const role = currentParams.get('role');
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            filteredData = filteredData.filter((app: any) => {
                const appDate = new Date(app.createdOn);
                return appDate >= start && appDate <= end;
            });
        }

        // 4. Filter by role (Agent/Agency)
        if (role) {
            filteredData = filteredData.filter((app: any) => (role === 'agent' ? app.accountType === AGENT_ACCOUNT_TYPE.INDIVIDUAL : app.accountType !== AGENT_ACCOUNT_TYPE.INDIVIDUAL));
        }

        setFetchedAgentApplications(filteredData);
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    };

    const updateApplicationStatusApproved = async (id: number, status: string) => {
        if (!id || !status) {
            showMessage('Missing required data.', 'error');
            return;
        }

        setIsPrivate(false);
        setMissingFields([]);
        try {
            setLoader(true);
            setShowActionDropdown(null);

            const formData = new FormData();
            formData.append('status', status);
            formData.append('rejectionReason', '');
            formData.append('requiredFields', JSON.stringify(missingFields));
            formData.append('isPrivate', String(isPrivate));

            const requestOptions: RequestInit = {
                method: 'PUT',
                headers: new Headers(),
                redirect: 'follow',
                credentials: 'include',
                body: formData,
            };

            const response = await fetch(`${API_ENDPOINTS.AGENT_APPLICATIONS}/${id || selectedRecordId}/status`, requestOptions);
            const result = await response.json();

            if (result.success) {
                setUpdateModalSuccess(true);
                setSuccessModalData({
                    title: 'Success',
                    description: `${!isPrivate ? 'Agent application status updated successfully.' : "'Agent application note added successfully.'"}`,
                });
                setIsPrivate(false);
                fetchData();
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoader(false);
        }
    };

    const updateApplicationStatus = async (id: number) => {
        if (!id) {
            showMessage('Missing required data.', 'error');
            return;
        }

        console.log(`Updating application status for ID: ${id}`, formValues);
        try {
            setLoader(true);
            setShowActionDropdown(null);

            if (((formValues.status && formValues.status.trim().toLowerCase() === 'rejected') || isPrivate) && formValues.rejectionReason.trim() === '') {
                setUpdateModalReason(true);
                setReasonError('Reason field is required!');
                setLoader(false);
                return;
            }

            setUpdateModalReason(false);
            const formData = new FormData();
            formData.append('status', formValues.status);
            formData.append('rejectionReason', formValues.rejectionReason);
            formData.append('requiredFields', JSON.stringify(missingFields));
            formData.append('isPrivate', String(isPrivate));

            const requestOptions: RequestInit = {
                method: 'PUT',
                headers: new Headers(),
                redirect: 'follow',
                credentials: 'include',
                body: formData,
            };

            const response = await fetch(`${API_ENDPOINTS.AGENT_APPLICATIONS}/${id || selectedRecordId}/status`, requestOptions);
            const result = await response.json();

            if (result.success) {
                setUpdateModalSuccess(true);
                setSuccessModalData({
                    title: 'Success',
                    description: `${!isPrivate ? 'Agent application status updated successfully.' : "'Agent application note added successfully.'"}`,
                });
                setIsPrivate(false);
                fetchData();
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoader(false);
        }
    };

    const handleAgentApplicationStatusUpdate = async (id: number, status: string) => {
        setSelectedRecordId(id);
        setFormValues({
            status,
            rejectionReason: '',
        });
        setReasonError('');
        setIsPrivate(false);
        if (status && status.trim().toLowerCase() === 'rejected') {
            setShowActionDropdown(null);
            await fetchReasonsForApplication(id);
            setUpdateModalReason(true);
            return;
        }

        updateApplicationStatus(id);
    };

    const fieldLabels = userType === AGENT_ACCOUNT_TYPE.INDIVIDUAL ? IndividualAgentFieldLabels : CompanyAgentFieldLabels;

    const handleCheckboxChange = (field: string) => {
        setMissingFields((prev) => (prev.includes(field) ? prev.filter((f) => f !== field) : [...prev, field]));
    };

    const addNotesModal = async (applicatioId: number) => {
        setSelectedRecordId(applicatioId);
        setIsPrivate(true);
        setShowActionDropdown(null);
        setReasonError('');
        setFormValues({
            status: '',
            rejectionReason: '',
        });
        await fetchReasonsForApplication(applicatioId);
        setUpdateModalReason(true);
    };

    return (
        <>
            {loader && <Loading />}
            <Modal isOpen={updateModalSuccess} onClose={() => setUpdateModalSuccess(false)}>
                <SuccessfullyDeleted
                    onClose={() => {
                        setUpdateModalSuccess(false);
                        setSuccessModalData({
                            title: '',
                            description: '',
                        });
                    }}
                    title={successModalData.title}
                    desc={successModalData.description}
                />
            </Modal>

            <Modal isOpen={updateModalReason} onClose={() => setUpdateModalReason(false)} classes="!max-w-3xl">
                <div className="mb-5 w-full font-inter">
                    {/* Reason History */}
                    <div className="px-6 py-4">
                        <h3 className="mb-2 text-base font-semibold text-[#2d2d2e]">Comment History</h3>
                        <div className="space-y-3">
                            {reasons.length > 0 ? (
                                reasons
                                    .filter((r) => r.isPrivate === isPrivate)
                                    .map((reasonItem) => {
                                        const isExpanded = expandedReasonId === reasonItem.id;
                                        const displayText = isExpanded ? reasonItem.reason : reasonItem.reason.length > 50 ? `${reasonItem.reason.substring(0, 50)}...` : reasonItem.reason;

                                        return (
                                            <div key={reasonItem.id} className="rounded-md border border-gray-200 bg-gray-50 p-3">
                                                <div className="flex items-center justify-between">
                                                    <span className="font-semibold text-[#2d2d2e]">
                                                        {reasonItem.firstName} {reasonItem.lastName}
                                                    </span>
                                                    <div className="flex items-center gap-2">
                                                        {reasonItem.created_at && <span className="text-xs text-[#888]">{new Date(reasonItem.created_at).toLocaleString()}</span>}
                                                    </div>
                                                </div>

                                                <div className="mt-1 flex items-center gap-2 text-xs italic text-[#666]">
                                                    {reasonItem.oldStatusName && <span className="font-medium text-[#444]">{reasonItem.oldStatusName}</span>}

                                                    {reasonItem.oldStatusName && reasonItem.newStatusName && reasonItem.oldStatusName !== reasonItem.newStatusName && (
                                                        <>
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                className="h-4 w-4 text-[#888]"
                                                                fill="none"
                                                                viewBox="0 0 24 24"
                                                                stroke="currentColor"
                                                                strokeWidth={2}
                                                            >
                                                                <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                                                            </svg>
                                                            <span className="font-medium text-[#444]">{reasonItem.newStatusName}</span>
                                                        </>
                                                    )}

                                                    {reasonItem.oldStatusName && reasonItem.newStatusName && reasonItem.oldStatusName === reasonItem.newStatusName && (
                                                        <span className="font-medium text-[#444]">(current)</span>
                                                    )}
                                                </div>

                                                <div
                                                    className={`transition-max-height mt-1 overflow-hidden whitespace-pre-wrap break-words text-sm text-[#555] duration-300 ease-in-out ${
                                                        isExpanded ? 'max-h-[1000px]' : 'max-h-[100px]'
                                                    }`}
                                                >
                                                    {displayText}
                                                </div>
                                                {reasonItem.reason.length > 50 && (
                                                    <button
                                                        onClick={() => setExpandedReasonId(isExpanded ? null : reasonItem.id)}
                                                        className="mt-1 text-sm text-blue-500 hover:underline focus:outline-none"
                                                    >
                                                        {isExpanded ? 'Show Less' : 'Show More'}
                                                    </button>
                                                )}
                                            </div>
                                        );
                                    })
                            ) : (
                                <p className="text-sm text-[#888]">No comment history found.</p>
                            )}
                        </div>
                    </div>

                    {/* Reason TextArea */}
                    <div className="flex flex-col px-6 pt-4">
                        <p className="text-lg font-bold text-[#2d2d2e]">Reason</p>
                        <div className="grid grid-cols-1 gap-5">
                            <TextAreaInput
                                id="rejection-reason"
                                label=""
                                value={formValues.rejectionReason}
                                onChange={(e) =>
                                    setFormValues({
                                        ...formValues,
                                        rejectionReason: e.target.value,
                                    })
                                }
                                placeholder="Reason*"
                            />
                            <span className="text-sm text-red-600">{reasonError}</span>
                        </div>
                    </div>

                    {/* Missing Fields */}
                    {formValues.status == 'Rejected' && (
                        <div className="px-6 pb-3">
                            <p className="py-4 text-sm font-medium text-gray-700">Missing / Invalid Fields ({userType === AGENT_ACCOUNT_TYPE.INDIVIDUAL ? 'Agent' : 'Agency'}):</p>
                            <div className="grid grid-cols-2 gap-2">
                                {Object.entries(fieldLabels).map(([key, label]) => (
                                    <label key={key} className="flex cursor-pointer items-center text-sm">
                                        <input type="checkbox" className="mr-2 cursor-pointer" checked={missingFields.includes(key)} onChange={() => handleCheckboxChange(key)} />
                                        {label}
                                    </label>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex justify-end border-t border-gray-200 px-6 py-4">
                        <button
                            onClick={() => updateApplicationStatus(selectedRecordId!)}
                            className="flex h-[46px] w-40 cursor-pointer items-center justify-center gap-2.5 rounded-md bg-blueMain px-3 text-base font-normal text-white hover:border hover:border-blueMain hover:bg-white hover:text-blueMain"
                        >
                            Submit
                        </button>
                    </div>
                </div>
            </Modal>

            <div className="mb-8 rounded-lg bg-white pt-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-3 lg:grid-cols-5">
                    {documentCounts.map((doc, index) => (
                        <div
                            onClick={(e) => {
                                e.preventDefault();
                                doc.type === 'Total Documents' ? setSelectedStatus('All') : setSelectedStatus(doc.type);
                            }}
                            key={`${index}-${doc.id}`}
                            className={`flex shrink grow basis-0 cursor-pointer items-center justify-between rounded-[20px] border-2 p-5 shadow-[0px_4px_20px_0px_rgba(21,32,70,0.05)] transition
                                    ${selectedStatus === (doc.type === 'Total Documents' ? 'All' : doc.type) ? 'border-blue-500 bg-white' : 'border-[#f0f0f0] bg-[#f8f9fa]'}`}
                        >
                            <div className="flex flex-col items-start justify-start gap-2">
                                <div className="font-golosText text-5xl font-bold" style={{ color: doc.color }}>
                                    {doc.count}
                                </div>
                                <div className="font-inter text-base font-medium text-[#555]">{doc.type}</div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            <div className="flex items-center justify-between py-4">
                <div className="flex w-full flex-col gap-5 md:items-center md:justify-between lg:flex-row lg:gap-0">
                    <div className="flex w-full flex-col flex-wrap items-center gap-5 md:flex-row ">
                        <div className="w-full min-w-[287px] flex-1 md:max-w-[287px] ">
                            <SearchInput onChange={handleSearchChange} value={searchTerm} placeholder="Search agents..." />
                        </div>

                        <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                            <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdown} initail={selectedStatus} setSelectedStatus={setSelectedStatus} />
                        </div>

                        <div className="w-auto min-w-[287px] flex-1 md:max-w-[287px]">
                            <SearchDropDown classes="!h-14 w-full" dropdownOptions={dropdownAccountTypes} initail={selectedType} setSelectedStatus={setSelectedType} />
                        </div>
                    </div>
                </div>
            </div>

            <div className=" mt- pb-4 font-inter font-normal  leading-[30px]">
                <span className="text-[#636363]">You have </span>
                <span className="text-[#993333]">{agentApplications.length} Agent Application Requests</span>
            </div>

            <div className="bg-white  ">
                <div ref={tableContainerRef} className="relative rounded-lg rounded-tl-lg w-full overflow-auto rounded-tr-lg border shadow-[0px_4px_20px_0px_rgba(21,32,70,0.07)]">
                    <table className="w-full border-collapse font-inter">
                        <thead className="sticky top-0 z-[10] bg-[#e4e4e4]">
                            <tr className="w-full rounded-lg rounded-tl-lg rounded-tr-lg border-none">
                                {columns.map((column) => (
                                    <th
                                        key={column.key}
                                        className={`h-[72px] cursor-pointer border-none px-4 py-4 text-left font-inter text-base font-medium leading-normal text-[#2d2d2e] ${column.width}`}
                                        onClick={() => column.key !== 'action' && column.key !== 'srno' && requestSort(column.key)}
                                    >
                                        <span className="flex items-center">
                                            {column.label}
                                            {column.key !== 'action' && column.key !== 'srno' && getSortIcon(column.key)}
                                        </span>
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody className="max-h-[700px] overflow-y-auto max-lg:overflow-x-auto">
                            {sortedAgentApplications.length === 0 && !loader && (
                                <tr>
                                    <td colSpan={8}>
                                        <div className="flex items-center justify-center py-10 text-center text-base font-medium text-[#888]">No Records to Show</div>
                                    </td>
                                </tr>
                            )}

                            {sortedAgentApplications.length != 0 &&
                                sortedAgentApplications.slice(page * agentApplicationsPerPage, (page + 1) * agentApplicationsPerPage).map((agentApplication, index) => (
                                    <tr key={agentApplication.id} className="text-grayText border-b border-[#E4E4E4] text-center hover:bg-gray-50">
                                        <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[0].width}`}>{index + 1}</td>
                                        <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[1].width}`}>
                                            {agentApplication.accountType === AGENT_ACCOUNT_TYPE.INDIVIDUAL
                                                ? `${agentApplication.firstName || ''} ${agentApplication.middleName || ''} ${agentApplication.lastName || ''}`.trim()
                                                : agentApplication.name}
                                        </td>
                                        <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#1D7EB6] ${columns[2].width}`}>{agentApplication.email}</td>
                                        <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#1D7EB6] ${columns[3].width}`}>{agentApplication.phone}</td>
                                        <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[4].width}`}>
                                            {agentApplication.accountType === AGENT_ACCOUNT_TYPE.INDIVIDUAL ? 'Agent' : 'Agency'}
                                        </td>
                                        <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[5].width}`}>
                                            {new Date(agentApplication.createdOn).toLocaleDateString('en-GB', {
                                                day: '2-digit',
                                                month: 'short',
                                                year: 'numeric',
                                            })}
                                        </td>
                                        <td className={`h-[72px] border-b border-t border-[#e4e4e4] px-4 py-4 text-sm font-normal text-[#636363] ${columns[6].width}`}>
                                            <StatusBadge status={agentApplication.currentStatusName} />
                                        </td>

                                        <td className={`relative px-4 py-4 ${columns[7].width}`}>
                                            <button
                                                ref={(el:any) => el && actionButtonRefs.current.set(agentApplication.id, el)}
                                                className="text-blue-500"
                                                onClick={(e) => handleActionClick(agentApplication.id, e)}
                                            >
                                                <ActionIcon />
                                            </button>
                                            {showActionDropdown === agentApplication.id && (
                                                <div
                                                    ref={actionDropdownRef}
                                                    className={`absolute right-0 z-[999999] w-40 rounded-md border border-gray-200 bg-white shadow-lg ${
                                                        dropdownPosition === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'
                                                    }`}
                                                >
                                                    <div className="py-1">
                                                        <span
                                                            onClick={() => push(`/dashboard/agents/application-preview/${agentApplication.id}`)}
                                                            className="block w-full cursor-pointer px-4 py-2 text-center text-sm text-[#2D2D2E] hover:bg-gray-100"
                                                        >
                                                            View Application
                                                        </span>

                                                        <span
                                                            className={`block w-full cursor-pointer px-4 py-2 text-center text-sm hover:bg-gray-100 ${
                                                                agentApplication.currentStatusName == 'Activated' ? '!cursor-not-allowed !text-gray-400' : ''
                                                            }`}
                                                            onClick={() => {
                                                                updateApplicationStatusApproved(agentApplication.id, 'Activated');
                                                            }}
                                                        >
                                                            Approved
                                                        </span>
                                                        <span
                                                            className={`block w-full cursor-pointer px-4 py-2 text-center text-sm hover:bg-gray-100 ${
                                                                agentApplication.currentStatusName == 'Rejected' ? '!cursor-not-allowed !text-gray-400' : ''
                                                            }`}
                                                            onClick={() => {
                                                                setUserType(agentApplication.accountType === AGENT_ACCOUNT_TYPE.INDIVIDUAL ? 'Agent' : 'Agency');
                                                                handleAgentApplicationStatusUpdate(agentApplication.id, 'Rejected');
                                                            }}
                                                        >
                                                            Rejected
                                                        </span>

                                                        <span
                                                            className={`block w-full cursor-pointer px-4 py-2 text-center text-sm hover:bg-gray-100`}
                                                            onClick={() => {
                                                                addNotesModal(agentApplication.id);
                                                            }}
                                                        >
                                                            Note
                                                        </span>
                                                        <button
                                                            onClick={() => handleAgentApplicationStatusUpdate(agentApplication.id, 'Suspended')}
                                                            className="block w-full px-4 py-2 text-center text-sm text-[#993333] hover:bg-gray-100"
                                                        >
                                                            Block Account
                                                        </button>
                                                    </div>
                                                </div>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                        </tbody>
                    </table>
                </div>

                {sortedAgentApplications.length != 0 && !loader && (
                    <div className="flex flex-col items-center gap-4 border-t border-[#E4E4E4] p-4 md:flex-row md:justify-between">
                        <div className="flex items-center gap-2">
                            <button onClick={handlePrev} disabled={page === 0} className="rounded-md p-2 disabled:opacity-50">
                                <PaginationRightIcon />
                            </button>
                            <div className="flex space-x-1">
                                {paginationRange().map((i) => (
                                    <button key={i} onClick={() => goToPage(i)} className={`rounded-md px-3 py-1 ${page === i ? 'bg-[#1D7EB6] text-white' : 'hover:bg-gray-100'}`}>
                                        {i + 1}
                                    </button>
                                ))}
                            </div>
                            <button onClick={handleNext} disabled={page === totalPages - 1} className="rounded-md p-2 disabled:opacity-50">
                                <PaginationLeftIcon />
                            </button>
                        </div>
                        <div className="relative flex items-center gap-2">
                            <span className="text-sm text-gray-500">Showing</span>
                            <div className="relative" ref={dropdownRef}>
                                <button className="flex items-center gap-1 rounded-md border border-gray-200 bg-[#EDF5F9] px-2 py-1 text-sm" onClick={() => setShowDropdown(!showDropdown)}>
                                    {agentApplicationsPerPage}
                                    <PaginationDownIcon />
                                </button>
                                {showDropdown && (
                                    <div className="absolute left-0 z-10 -mt-40 w-16 rounded-md border border-gray-200 bg-white shadow-lg">
                                        <div className="py-1">
                                            {[10, 20, 50].map((value) => (
                                                <button
                                                    key={value}
                                                    className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                                    onClick={() => {
                                                        setAgentApplicationsPerPage(value);
                                                        setShowDropdown(false);
                                                        setPage(0); // Reset to first page when changing items per page
                                                    }}
                                                >
                                                    {value}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                            <span className="text-sm text-gray-500">Applications out of {agentApplications.length}</span>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}
